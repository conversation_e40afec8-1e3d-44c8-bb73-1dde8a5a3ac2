package com.xiaomi.growth.feature.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaomi.growth.feature.constants.EnumConstant;
import com.xiaomi.growth.feature.model.inapi.BaseReq;
import com.xiaomi.growth.feature.model.inapi.Result;
import com.xiaomi.growth.feature.model.inapi.req.DataSourceListReq;
import com.xiaomi.growth.feature.model.inapi.req.FeatureListReq;
import com.xiaomi.growth.feature.model.inapi.rsp.DataSourceInfoRsp;
import com.xiaomi.growth.feature.model.inapi.rsp.FeatureInfoRsp;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xiaomi.growth.feature.constants.EnumConstant.Env.SYSTEM_ENV;
import static com.xiaomi.growth.feature.utils.HttpUtils.MEDIA_TYPE_APPLICATION_JSON;

;

/**
 * <AUTHOR>
 */
public class AlgoHubInternalService {

    public static final String URL_FEATURE_LIST = "/api/internal/feature/list";
    public static final String URL_DATASOURCE_LIST = "/api/internal/datasource/list";

    private static final AlgoHubInternalService PROD_INSTANCE;
    private static final AlgoHubInternalService STAGING_INSTANCE;

    private static final String DEFAULT_TOKEN = "c0227d1a00484c33947eca20bb9dec61";

    static {
        PROD_INSTANCE = new AlgoHubInternalService("http://algohub.growth.xiaomi.srv/");
        STAGING_INSTANCE = new AlgoHubInternalService("http://feature-store-dev.xiaomi.srv");
    }

    private final String domain;

    private AlgoHubInternalService(@NonNull String domain) {
        this.domain = domain;
    }

    public static AlgoHubInternalService getInstance(EnumConstant.Env env) {
        switch (env) {
            case PROD:
                return PROD_INSTANCE;
            case STAGING:
            default:
                return STAGING_INSTANCE;
        }
    }

    public static AlgoHubInternalService getInstance() {
        String env = System.getProperty(SYSTEM_ENV, EnumConstant.Env.STAGING.name());
        return getInstance(EnumConstant.Env.of(env));
    }

    public Result<List<FeatureInfoRsp>> getFeatureByNames(List<String> names) {
        FeatureListReq req = FeatureListReq.builder().nameList(names).build();
        return this.post(req, URL_FEATURE_LIST, new TypeReference<Result<List<FeatureInfoRsp>>>() {
        });
    }

    public Result<List<DataSourceInfoRsp>> getDataSourceById(List<Long> ids) {
        DataSourceListReq req = DataSourceListReq.builder().idList(ids).build();
        return this.post(req, URL_DATASOURCE_LIST, new TypeReference<Result<List<DataSourceInfoRsp>>>() {
        });
    }

    public <T> Result<T> post(BaseReq req, String url, TypeReference<Result<T>> responseType) {
        Map<String, String> header = new HashMap<String, String>() {{
            put(HttpUtils.CONTENT_TYPE, MEDIA_TYPE_APPLICATION_JSON);
        }};
        req.setToken(DEFAULT_TOKEN);
        String resp = HttpUtils.post(this.domain + url, header, JsonUtils.toJson(req));
        if (StringUtils.isEmpty(resp)) {
            throw new RuntimeException("post request failed! url: " + url);
        }
        return JsonUtils.fromJson(resp, responseType);
    }

}
