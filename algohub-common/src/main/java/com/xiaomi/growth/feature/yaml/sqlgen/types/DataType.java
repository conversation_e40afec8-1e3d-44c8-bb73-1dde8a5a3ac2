package com.xiaomi.growth.feature.yaml.sqlgen.types;

import lombok.NonNull;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public interface DataType extends Serializable {

    /**
     * 类型名称
     *
     * @return 数据类型的类型名称
     */
    String typeName();

    /**
     * 类型分类
     *
     * @return @see DataTypeCategory
     */
    DataTypeCategory typeCategory();


    /**
     * 是否接受该对象
     *
     * @param object 对象
     * @return 是否接受该对象
     */
    boolean acceptsObject(@NonNull Object object);

    BasicDataType.BooleanType BOOLEAN = new BasicDataType.BooleanType();
    BasicDataType.ByteType BYTE = new BasicDataType.ByteType();
    BasicDataType.ShortType SHORT = new BasicDataType.ShortType();
    BasicDataType.IntType INT = new BasicDataType.IntType();
    BasicDataType.LongType LONG = new BasicDataType.LongType();
    BasicDataType.FloatType FLOAT = new BasicDataType.FloatType();
    BasicDataType.DoubleType DOUBLE = new BasicDataType.DoubleType();
    BasicDataType.StringType STRING = new BasicDataType.StringType();
    BasicDataType.BinaryType BINARY = new BasicDataType.BinaryType();


    /**
     * 创建List类型
     *
     * @param itemType 元素类型
     * @return List类型
     */
    static <T extends DataType> ContainerDataType.ListType<T> createListType(T itemType) {
        return new ContainerDataType.ListType<>(itemType);
    }

    /**
     * 创建Map类型
     *
     * @param keyType   key类型
     * @param valueType value类型
     * @return Map类型
     */
    static <K, V extends DataType> ContainerDataType.MapType<K, V> createMapType(K keyType, V valueType) {
        return new ContainerDataType.MapType<>(keyType, valueType);
    }

    /**
     * 创建Struct类型
     *
     * @param structClass struct类
     * @param fieldsType  字段类型
     * @return Struct类型
     */
    static StructType createStructType(Class<?> structClass, DataType... fieldsType) {
        return new StructType(structClass, fieldsType);
    }

    /**
     * 创建Enum类型
     *
     * @param enumClass enum类
     * @return Enum类型
     */
    static EnumType createEnumType(Class<?> enumClass) {
        return new EnumType(enumClass);
    }

    default org.jooq.DataType<?> toJooqDataType(){
        throw new UnsupportedOperationException("JOOQ类型转换暂不支持此类型: " + this.typeName());
    }
}
