package com.xiaomi.growth.feature.model.job;

import java.io.Serializable;


public abstract class BaseJobParam implements Serializable {
    // 作业类型
    private String jobType;
    // 作业名称
    private String jobName;
    // 作业负责人
    private String owner;
    // 重试次数
    private int retryTimes = 2;
    // 失败重试的间隔，分钟
    private int retryIntervals = 30;
    // 作业描述
    private String description;
    // workflowId
    private String workflowId;
    // 运行模式
    private String mode = "SAVE";


    public BaseJobParam() {
    }

    public BaseJobParam(String jobType, String jobName, String owner, String description) {
        this.jobType = jobType;
        this.jobName = jobName;
        this.owner = owner;
        this.description = description;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getRetryIntervals() {
        return retryIntervals;
    }

    public void setRetryIntervals(int retryIntervals) {
        this.retryIntervals = retryIntervals;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }
}
