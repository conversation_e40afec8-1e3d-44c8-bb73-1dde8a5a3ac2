package com.xiaomi.growth.feature.model.workflow.rsp;

import com.xiaomi.growth.feature.model.workflow.Cron;
import com.xiaomi.growth.feature.model.workflow.EdgeModel;
import com.xiaomi.growth.feature.model.workflow.JobDagModel;
import com.xiaomi.growth.feature.model.workflow.Notify;
import lombok.Data;

import java.util.List;

@Data
public class WorkFlowDetail {
    private String id;
    private String projectId;
    private String workflowId;
    private String workflowName;
    private String version;
    private String versionTag;
    private String modifier;
    private String comments;
    private Cron cron;
    private List<Notify> noticeList;
    private JobDagModel jobDag;
    private EdgeModel edges;
    private String quartzCron;
}
