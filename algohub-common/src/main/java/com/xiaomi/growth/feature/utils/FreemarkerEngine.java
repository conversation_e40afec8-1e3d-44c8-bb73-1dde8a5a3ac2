package com.xiaomi.growth.feature.utils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * freemarker模板引擎工具类
 */
@Getter
public class FreemarkerEngine {

    /**
     * 中括号语法
     */
    public static final Configuration CONFIG_SQUARE_BRACKET_SYNTAX;

    static {
        CONFIG_SQUARE_BRACKET_SYNTAX = new Configuration(Configuration.VERSION_2_3_31);
        CONFIG_SQUARE_BRACKET_SYNTAX.setInterpolationSyntax(Configuration.SQUARE_BRACKET_INTERPOLATION_SYNTAX);
        CONFIG_SQUARE_BRACKET_SYNTAX.setTagSyntax(Configuration.SQUARE_BRACKET_TAG_SYNTAX);
    }

    private final Configuration cfg;

    public FreemarkerEngine() {
        cfg = new Configuration(Configuration.VERSION_2_3_31);
        // 设置默认编码格式
        cfg.setDefaultEncoding(StandardCharsets.UTF_8.name());
    }

    public FreemarkerEngine(Configuration cfg) {
        this.cfg = cfg;
    }

    public Template createTemplateFromFile(File file) throws IOException {
        if (file == null) {
            throw new IllegalArgumentException("file is null!");
        }
        return createTemplateFromReader(new FileReader(file), file.getName());
    }

    public Template createTemplateFromReader(Reader reader, String tempName) throws IOException {
        if (reader == null) {
            throw new IllegalArgumentException("reader is null!");
        }
        return new Template(tempName, reader, cfg);
    }

    public Template createTemplateFromContent(String tempContent, String tempName) throws IOException {
        if (StringUtils.isBlank(tempContent)) {
            throw new IllegalArgumentException("tempContent is blank!");
        }
        return createTemplateFromReader(new StringReader(tempContent), tempName);
    }


    public static void processTemplate(Template template, Map<String, Object> dataModel, Writer writer) throws RuntimeException {
        if (template == null) {
            throw new IllegalArgumentException("template is null!");
        }
        try {
            template.process(dataModel, writer);
        } catch (Exception e) {
            throw new RuntimeException("模板解析失败", e);
        }
    }

    public static String processTemplateToString(Template template, Map<String, Object> dataModel) throws RuntimeException {
        StringWriter writer = new StringWriter();
        processTemplate(template, dataModel, writer);
        return writer.toString();
    }

    public static void processTemplateToFile(Template template, Map<String, Object> dataModel, String outputPath) throws RuntimeException {
        FileWriter writer = null;
        try {
            writer = new FileWriter(outputPath);
        } catch (IOException e) {
            throw new RuntimeException("create FileWriter error, path: " + outputPath, e);
        }
        processTemplate(template, dataModel, writer);
    }

}
