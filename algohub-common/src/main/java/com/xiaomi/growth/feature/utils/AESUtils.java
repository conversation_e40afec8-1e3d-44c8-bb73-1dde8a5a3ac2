package com.xiaomi.growth.feature.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * AES加密解密工具类
 **/
public class AESUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(AESUtils.class);
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    public static String encrypt(String src, String key) throws Exception {
        if (StringUtils.isBlank(src)) {
            LOGGER.info("src为空");
            return null;
        }
        Cipher cipher = initCipher(key, Cipher.ENCRYPT_MODE);
        byte[] encrypted = cipher.doFinal(src.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public static String decrypt(String src, String key) throws Exception {
        if (StringUtils.isBlank(src)) {
            LOGGER.info("src为空");
            return null;
        }
        Cipher cipher = initCipher(key, Cipher.DECRYPT_MODE);

        byte[] encrypted = Base64.getDecoder().decode(src);

        byte[] original = Objects.requireNonNull(cipher).doFinal(encrypted);
        return new String(original, StandardCharsets.UTF_8);
    }

    private static Cipher initCipher(String key, int cipherModel) throws Exception {
        if (StringUtils.isBlank(key)) {
            LOGGER.info("key为空");
            return null;
        }
        if (key.length() != 16) {
            LOGGER.info("key长度不是16位");
            return null;
        }
        byte[] rawKey = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec keySpec = new SecretKeySpec(rawKey, "AES");
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(cipherModel, keySpec);
        return cipher;
    }


}
