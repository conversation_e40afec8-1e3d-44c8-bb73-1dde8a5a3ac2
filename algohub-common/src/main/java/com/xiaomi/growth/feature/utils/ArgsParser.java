package com.xiaomi.growth.feature.utils;

import picocli.CommandLine;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ArgsParser {

    public static String[] toArgs(Object obj) {
        CommandLine.Model.CommandSpec spec = CommandLine.Model.CommandSpec.forAnnotatedObject(obj);
        List<String> args = new ArrayList<>();
        for (CommandLine.Model.OptionSpec option : spec.options()) {
            if (option.hidden()) {
                continue;
            }

            Object value = option.getValue();
            if (value == null) {
                continue; // 跳过 null 值
            }
            // 获取选项名称（使用第一个非帮助选项）
            String optionName = Arrays.stream(option.names())
                    .filter(name -> !name.startsWith("-h"))
                    .findFirst()
                    .orElse(null);
            if (optionName == null) {
                continue;
            }
            // 处理布尔标志
            if (value instanceof Boolean && (Boolean) value) {
                args.add(optionName);
            } else if (value instanceof List) {
                List<?> list = (List<?>) value;
                for (Object item : list) {
                    args.add(optionName);
                    args.add(item.toString());
                }
            } else if (value.getClass().isArray()) {
                Object[] array = (Object[]) value;
                for (Object item : array) {
                    args.add(optionName);
                    args.add(item.toString());
                }
            } else if (value instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) value;
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    args.add(optionName);
                    args.add(entry.getKey().toString() + "=" + entry.getValue().toString());
                }
            } else {
                args.add(optionName);
                args.add(value.toString());
            }

        }
        return args.toArray(new String[0]);
    }

    public static <T> T fromArgs(String[] args, Class<T> clazz) {
        T instance;
        try {
            instance = clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("get new instance error", e);
        }
        fromArgs(args, instance);
        return instance;
    }

    public static void fromArgs(String[] args, Object obj) {
        CommandLine commandLine = new CommandLine(obj);
        commandLine.setCaseInsensitiveEnumValuesAllowed(true);
        commandLine.setUnmatchedArgumentsAllowed(true);
        commandLine.parseArgs(args);
    }

}