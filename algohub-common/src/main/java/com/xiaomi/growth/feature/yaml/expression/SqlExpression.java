package com.xiaomi.growth.feature.yaml.expression;

import lombok.Getter;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;

import static com.xiaomi.growth.feature.yaml.expression.FeatureReferenceExpression.FEATURE_REF_PATTERN;

/**
 * <AUTHOR>
 */

@Getter
public class SqlExpression extends BaseExpression {

    public final static Tag TAG_SQL_EXP = new Tag("!sql_exp");

    private final String sqlExp;

    private String resolverSqlExp;

    public SqlExpression(String sqlExp) {
        this.sqlExp = sqlExp;
    }

    public SqlExpression(String sqlExp, String guid) {
        this.sqlExp = sqlExp;
        this.setGuid(guid);
    }

    @Override
    public NodeId getNodeId() {
        return NodeId.scalar;
    }

    @Override
    public Tag getTag() {
        return TAG_SQL_EXP;
    }


    public Set<String> extractFeatureRefs() {
        Set<String> placeholders = new LinkedHashSet<>();
        // 定义正则表达式模式，匹配${feat.xxxx}格式
        Matcher matcher = FEATURE_REF_PATTERN.matcher(this.sqlExp);
        while (matcher.find()) {
            placeholders.add(matcher.group(1));
        }
        return placeholders;
    }

    @Override
    public Object resolvedExpression() {
        if (this.resolverSqlExp == null) {
            resolverSqlExp = getReplacedSqlExp();
        }
        return resolverSqlExp;
    }

    public String getReplacedSqlExp() {
        StringBuffer result = new StringBuffer();
        Matcher matcher = FEATURE_REF_PATTERN.matcher(this.sqlExp);
        while (matcher.find()) {
            matcher.appendReplacement(result, Matcher.quoteReplacement(matcher.group(1)));
        }

        matcher.appendTail(result);
        return result.toString();
    }
}
