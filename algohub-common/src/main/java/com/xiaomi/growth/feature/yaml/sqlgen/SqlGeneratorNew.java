package com.xiaomi.growth.feature.yaml.sqlgen;

import com.google.common.collect.Lists;
import com.xiaomi.growth.feature.yaml.expression.*;
import com.xiaomi.growth.feature.yaml.sqlgen.types.BasicDataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.ContainerDataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.DataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.StructType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Select;
import org.jooq.WithStep;
import org.jooq.conf.ParamType;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.xiaomi.growth.feature.yaml.sqlgen.SparkDSL.*;
import static com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver.buildFullPath;
import static org.jooq.impl.DSL.*;

/**
 * <AUTHOR>
 *
 * sql中将map修改为map_filter 过滤掉null值
 */
@Slf4j
public class SqlGeneratorNew {

    private static final Field<?> NULL_FIELD = field("NULL");

    public static final String PRIMARY_KEY_ALIAS = "id";
    public static final String FEATURE_LIB_ALIAS = "feature_lib";

    public static final String DATE_PARTITION_PLACEHOLDER = "#run_date#";

    public static List<String> generateUdfRefStatement(List<UdfDeclareExpression> udfList) {
        if (udfList == null || udfList.isEmpty()) {
            return Collections.emptyList();
        }
        return udfList.stream().map(udf -> create_temp_func(udf.getName(), udf.getClazz()).toString()).collect(Collectors.toList());
    }

    public static String generateQueryStatement(ParsedMappingConfig config) {
        // 这个函数的输入是 parsedMappingConfig

        // 1. 生成CTE部分 (WITH feature_lib AS (...))
        WithStep featureLibQueryPart = SPARK_DSL_CONTEXT.with(FEATURE_LIB_ALIAS).as(generateFeatureLibSelect(config));
        log.info("generate feature lib query part");
        // 2. select 的字段
        List<Field<?>> finalFields = generateFinalFields(config, config.getTypeMap());
        log.info("generate final fields");
        // 3. 组合成完整SQL并返回
        return featureLibQueryPart.select(finalFields).from(FEATURE_LIB_ALIAS).getSQL(ParamType.INLINED);
    }

    /**
     * 构造最终查询到字段，会根据制定的序列化类封装成对应的struct
     *
     * @param config  解析后的映射配置
     * @param typeMap 序列化类中每个属性对应的数据类型
     * @return 返回所有的字段
     */
    private static List<Field<?>> generateFinalFields(ParsedMappingConfig config, Map<String, DataType> typeMap) {
        List<Field<?>> finalFields = new LinkedList<>(); // 返回列表
        config.getOriginalMapping().forEach((k, v) -> {
            finalFields.add(generateField(v, typeMap, StringUtils.EMPTY, k).as(k));
        });
        return finalFields;
    }

    private static Field<?> generateMapField(Map<String, Object> value,
                                             Map<String, DataType> typeMap,
                                             ContainerDataType.MapType<?, ?> fieldType) {
        List<Pair<Field<String>, Field<?>>> keyValuePairs = new LinkedList<>();
        DataType valueTargetType = fieldType.getValueType();

        value.forEach((k, v) -> {
            Field<?> valueField = generateField(v, typeMap, fieldType.typeName(), k);

            // 递归处理嵌套类型
            if (valueTargetType instanceof ContainerDataType) {
                if (valueTargetType instanceof ContainerDataType.MapType) {
                    valueField = generateMapField((Map<String, Object>) v, typeMap,
                            (ContainerDataType.MapType<?, ?>) valueTargetType);
                } else if (valueTargetType instanceof ContainerDataType.ListType) {
                    valueField = generateListField((List<Object>) v, typeMap,
                            (ContainerDataType.ListType<?>) valueTargetType);
                }
            }
//            else if (valueTargetType instanceof BasicDataType) {
//                valueField = castToTargetType(valueField, valueTargetType);
//            }
            keyValuePairs.add(Pair.of(val(k), valueField));
        });
//        return map(keyValuePairs.toArray(new Pair[0]));
        Field<?> originalMap = map(keyValuePairs.toArray(new Pair[0]));
        return field("map_filter({0}, (k, v) -> v IS NOT NULL)", originalMap.getDataType(), originalMap);
    }

    @SuppressWarnings("unchecked")
    private static Field<?> generateStructField(Map<String, Object> value,
                                                Map<String, DataType> typeMap,
                                                String parentPath) {
        List<Pair<Field<String>, Field<?>>> nameValuePairs = new LinkedList<>();
        value.forEach((k, v) -> nameValuePairs.add(Pair.of(val(k), generateField(v, typeMap, parentPath, k))));
        return named_struct(nameValuePairs.toArray(new Pair[0]));
    }
    private static Field<?> generateListField(List<Object> value, Map<String, DataType> typeMap, ContainerDataType.ListType<?> fieldType) {
        List<Field<?>> elements = new LinkedList<>();
        DataType elementTargetType = fieldType.getElementType(); // 获取列表元素的目标类型
        value.forEach(i -> {
            // 生成元素字段，并进行类型转换
            Field<?> elementField = generateField(i, typeMap, fieldType.typeName(), TypeResolver.LIST_ITEM_TYPE_SUFFIX);
            if (elementTargetType instanceof BasicDataType) {
                elementField = castToTargetType(elementField, elementTargetType);
            }
            elements.add(elementField);
        });
        return spark_array(elements.toArray(new Field[0]));
    }

    private static Field<?> generateField(Object value, Map<String, DataType> typeMap, String parentPath, String fieldName) {
        if (value == null) {
            return NULL_FIELD;
        }
        String path = buildFullPath(parentPath, fieldName);
        DataType targetType = typeMap.get(path); // 获取目标类型
//        if (targetType == null) {
//            throw new RuntimeException("No target type found for path: " + path);
//        }
        // 如果未找到类型，尝试从Map类型名称查找值类型
        if (targetType == null) {
            DataType parentType = typeMap.get(parentPath);
            // 尝试找到 parentPath 对应的 MapType 字段（反查）
            Optional<Map.Entry<String, DataType>> mapEntry = typeMap.entrySet().stream()
                    .filter(e -> e.getValue() instanceof ContainerDataType.MapType)
                    .filter(e -> ((ContainerDataType.MapType<?, ?>) e.getValue()).typeName().equals(parentPath))
                    .findFirst();
            if (mapEntry.isPresent()) {
                String mapField = mapEntry.get().getKey();
                String mapPath = buildFullPath(parentPath, "__val_type__");
                targetType = typeMap.get(mapPath);
            }
            // 如果仍然找不到，抛出异常
            if (targetType == null) {
                throw new RuntimeException("No target type found for path: " + path);
            }
        }
        // 1. 生成原始字段（未转换类型）
        Field<?> rawField;
        if (value instanceof Map) {
            if (targetType instanceof StructType) {
                rawField = generateStructField((Map<String, Object>) value, typeMap, path);
            } else {
                rawField = generateMapField((Map<String, Object>) value, typeMap, (ContainerDataType.MapType<?, ?>) targetType);
            }
        } else if (value instanceof List) {
            rawField = generateListField((List<Object>) value, typeMap, (ContainerDataType.ListType<?>) targetType);
        } else if (value instanceof BaseExpression) {
            rawField = generateFieldByExpression((BaseExpression) value);
        } else {
            rawField = field(value.toString());
        }
        // 2. 对原始字段进行类型转换
        return castToTargetType(rawField, targetType);
    }
    /**
     * 将字段转换为目标类型（基于typeMap中的DataType）
     */
    private static Field<?> castToTargetType(Field<?> field, DataType targetType) {
        if (targetType instanceof BasicDataType) {
            // 基本类型：直接转换为jOOQ对应的DataType
            org.jooq.DataType<?> jooqType = ((BasicDataType) targetType).toJooqDataType();
            return field.cast(jooqType);
        } else {
            // 容器类型（Map/List等）：不直接转换外层，而是递归处理内部元素
            return field;
        }
    }

    private static Field<?> generateFieldByExpression(BaseExpression expression) {
        Field<?> exprField;
        if (expression instanceof SqlExpression) {
            exprField = field(expression.resolvedExpression().toString());
        } else if (expression instanceof SqlBlockExpression) {
            List<Field<?>> fields = new LinkedList<>();
            ((SqlBlockExpression) expression).getSqlExpressions().forEach(exp -> fields.add(generateFieldByExpression(exp)));
            exprField = spark_array(fields.toArray(new Field[0]));
        } else if (expression instanceof EnvVarExpression) {
            exprField = val(expression.resolvedExpression());
        } else if (expression instanceof PlaceholderExpression) {
            exprField = field(expression.resolvedExpression().toString());
        } else {
            exprField = val(expression);
        }
        return exprField;
    }
    /**
     * 构造所有用到的特征的查询语句
     *
     * @param config 解析后的映射配置
     * @return 返回select对象
     */
    private static Select<?> generateFeatureLibSelect(ParsedMappingConfig config) {
        // 这个函数的输入是 parsedMappingConfig
        // tableFeaturesMap 是 表到特征字段的映射配置
        Map<ParsedMappingConfig.Tbl, Set<Pair<String, String>>> tableFeaturesMap = config.getTableFeaturesMap(); // Tbl - (表中字段名,特征名)
        if (tableFeaturesMap.isEmpty()) {
            throw new RuntimeException("No table feature found!");
        }
        int total = config.getFeatureInfoMap().size();  // 特征名- FeatureInfoRsp 特征的个数
        int inx = 0;
        int[] range = IntStream.range(0, total).toArray();
        Select<Record> select = null; // 用于累积多表查询结果
        // 预收集所有特征
        List<Pair<String, String>> allFeatures = new LinkedList<>();
        for (Map.Entry<ParsedMappingConfig.Tbl, Set<Pair<String, String>>> entry : tableFeaturesMap.entrySet()) {
            List<Pair<String, String>> features = Lists.newArrayList(entry.getValue());
            allFeatures.addAll(features);
        }
        for (Map.Entry<ParsedMappingConfig.Tbl, Set<Pair<String, String>>> entry : tableFeaturesMap.entrySet()) {
            ParsedMappingConfig.Tbl table = entry.getKey();
            List<Pair<String, String>> features = Lists.newArrayList(entry.getValue());
            List<Field<?>> featureFields = new LinkedList<>(); // 记录当前表需要查询的所有字段（包括主键和特征字段），并且通过 NULL 填充确保所有表的查询字段数量和顺序完全一致（用于后续 UNION ALL 合并）
            featureFields.add(field(table.getPrimaryKey()).as(PRIMARY_KEY_ALIAS));
            for (int i : range) {
                if (i >= inx && i < inx + features.size()) {
                    int localIndex = i - inx;
                    featureFields.add(field(features.get(localIndex).getLeft()).as(features.get(localIndex).getRight()));

                } else {
                     featureFields.add(NULL_FIELD.as(allFeatures.get(i).getRight()));
                }
            }
            inx += features.size();
            Select<Record> currentSelect = SPARK_DSL_CONTEXT
                    .select(featureFields)
                    .from(table.getTableName())
                    .where(field(table.getPartitionField()).eq(field(DATE_PARTITION_PLACEHOLDER)));
            select = select == null ? currentSelect : select.unionAll(currentSelect);
        }
        if (tableFeaturesMap.size() == 1) {
            return select;
        }
        // 最外层聚合所有特征
        List<Field<?>> finalFields = allFeatures.stream().map(p -> max(field(p.getRight())).as(p.getRight())).collect(Collectors.toList());

        finalFields.add(0, field(PRIMARY_KEY_ALIAS));
        return SPARK_DSL_CONTEXT.select(finalFields).from(select).groupBy(field(PRIMARY_KEY_ALIAS));
    }

}
