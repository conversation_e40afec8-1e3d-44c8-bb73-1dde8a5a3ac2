package com.xiaomi.growth.feature.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {
    public static final String AUTH = "Authorization";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String MEDIA_TYPE_APPLICATION_JSON = "application/json";
    private static PoolingHttpClientConnectionManager pool;

    /**
     * 全局请求配置
     */
    private static final RequestConfig REQUEST_CONFIG = RequestConfig.custom()
            .setConnectTimeout(5000)
            .setSocketTimeout(10000)
            .setConnectionRequestTimeout(10000)
            .build();

    /**
     * 线程安全的HttpClient实例
     */
    private static final CloseableHttpClient HTTP_CLIENT = HttpClientBuilder.create()
            .setConnectionManager(pool)
            .setDefaultRequestConfig(REQUEST_CONFIG)
            .build();

    static {
        pool = new PoolingHttpClientConnectionManager(10, TimeUnit.SECONDS);
        pool.setMaxTotal(200);
        pool.setDefaultMaxPerRoute(50);
    }

    public static String post(String url, Map<String, String> headers, String param) {
        HttpPost httpPost = new HttpPost(url);
        setHeaders(httpPost, headers);
        if (param != null) {
            httpPost.setEntity(new StringEntity(param, StandardCharsets.UTF_8));
        }

        try (CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                log.info("HttpUtils post success url:{}, param:{}, response:{}", url, param, entity);
                return entity;
            } else {
                String entity = EntityUtils.toString(response.getEntity());
                log.error("HttpUtils post error url:{}, code:{}, entity:{}", url, response.getStatusLine().getStatusCode(), entity);
            }
        } catch (Exception e) {
            log.error("HttpUtils post error url:{}", url, e);
        }
        return null;
    }

    public static String postForm(String url, Map<String, String> headers, Map<String, String> formParams) {
        HttpPost httpPost = new HttpPost(url);
        setHeaders(httpPost, headers);
        List<NameValuePair> params = new ArrayList<>();
        for (Map.Entry<String, String> entry : formParams.entrySet()) {
            params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }

        // 设置请求体
        httpPost.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));
        try (CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                log.info("HttpUtils post success url:{}, response:{}", url, entity);
                return entity;
            } else {
                String entity = EntityUtils.toString(response.getEntity());
                log.error("HttpUtils post error url:{}, code:{}, entity:{}", url, response.getStatusLine().getStatusCode(), entity);
            }
        } catch (Exception e) {
            log.error("HttpUtils post error url:{}", url, e);
        }
        return null;
    }


    public static String put(String url, Map<String, String> headers, String param) {
        HttpPut httpPut = new HttpPut(url);
        setHeaders(httpPut, headers);
        if (param != null) {
            httpPut.setEntity(new StringEntity(param, StandardCharsets.UTF_8));
        }

        try (CloseableHttpResponse response = HTTP_CLIENT.execute(httpPut)) {
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity());
                log.info("HttpUtils put success url:{}, param:{}, response:{}", url, param, entity);
                return entity;
            } else {
                String entity = EntityUtils.toString(response.getEntity());
                log.error("HttpUtils put error url:{}, code:{}, entity:{}", url, response.getStatusLine().getStatusCode(), entity);
            }
        } catch (Exception e) {
            log.error("HttpUtils put error url:{}", url, e);
        }
        return null;
    }

    public static String get(String url, Map<String, String> headers) {
        HttpGet httpGet = new HttpGet(url);
        setHeaders(httpGet, headers);

        try (CloseableHttpResponse response = HTTP_CLIENT.execute(httpGet)) {
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                log.info("HttpUtils get success url:{}, response:{}", url, entity);
                return entity;
            } else {
                log.error("HttpUtils get error url:{}, code:{}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("HttpUtils get error url:{}", url, e);
        }
        return null;
    }

    /**
     * 设置请求头
     *
     * @param request 请求
     * @param headers 请求头
     */
    private static void setHeaders(HttpRequestBase request,
                                   Map<String, String> headers) {
        if (headers != null) {
            headers.forEach(request::setHeader);
        }
    }

}
