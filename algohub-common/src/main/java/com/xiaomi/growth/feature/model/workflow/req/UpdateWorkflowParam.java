package com.xiaomi.growth.feature.model.workflow.req;

import com.xiaomi.growth.feature.model.workflow.JobDagModel;
import com.xiaomi.growth.feature.model.workflow.Notify;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateWorkflowParam {
    private String projectId;
    private String workflowName;
    private String comments;
    private Object cron;
    private String quartzCron;
    private String cronConfigType = "CUSTOM";
    private List<Notify> noticeList;
    private JobDagModel jobDag;
    private boolean enableSchedule;
    private String updateType = "INCREASE_VERSION";
    private String description;
}
