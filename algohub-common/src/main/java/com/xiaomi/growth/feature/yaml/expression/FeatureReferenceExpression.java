package com.xiaomi.growth.feature.yaml.expression;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class FeatureReferenceExpression extends PlaceholderExpression {

    public final static Pattern FEATURE_REF_PATTERN = Pattern.compile("\\$\\{" + PlaceholderType.FEATURE.token + "\\.([^}]+)}");

    public FeatureReferenceExpression(String originExp) {
        super(originExp);
        if (!PlaceholderType.FEATURE.equals(this.getType())) {
            // 类型不匹配，抛出异常
            throw new IllegalArgumentException("expression type is not match, required: FEATURE, actual: " + this.getType());
        }
    }

}
