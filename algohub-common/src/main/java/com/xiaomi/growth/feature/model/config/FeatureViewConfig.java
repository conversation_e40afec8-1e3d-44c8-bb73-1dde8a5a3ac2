package com.xiaomi.growth.feature.model.config;

import com.xiaomi.growth.feature.model.job.BaseJobParam;
import com.xiaomi.growth.feature.yaml.expression.UdfDeclareExpression;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FeatureViewConfig {
    //---------------base config ----------------------
    /**
     * 基础信息
     */
    private BaseInfo baseInfo;
    /**
     * 作业信息
     */
    private List<BaseJobParam> jobList;

    /**
     * 外部依赖的job, 基础视图不需要，复合视图传基础视图的jobId
     */
    private List<DependentJob> dependentJobs;

    //---------------process config ----------------------
    /**
     * schema信息
     */
    private Schema schema;
    /**
     * 数据源信息
     */
    private List<OnlineDatasourceConfig> datasourceConfig;

    /**
     * 特征映射配置
     */
    private MappingConfig mappingConfig;


    @Data
    public static class MappingConfig {

        private List<UdfDeclareExpression> udf;

        private Map<String, Object> mapping;
    }

    @Data
    public static class BaseInfo {
        private long viewId;
        private String version;
        private String viewName;
        private String projectName;
        private String featureEntity;
        private String featurePrimaryKey;
        private Integer viewType;
        private String viewDesc;
        private int lifeCycle;
        private String creator;
        private List<String> dependencyTableName;
        private String baseConfigName;
        private String processConfigName;
        private long createTs;
        private long updateTs;
        private String quartzCron;
    }

    @Data
    public static class Schema {

        private String namespace;

        private String className;

        private String serializeClassName;

        private String owner;

        private MavenInfo mavenInfo;
    }

    @Data
    public static class MavenInfo {

        private String groupId;

        private String artifactId;

        private String version;
    }

    @Data
    public static class OnlineDatasourceConfig {

        private boolean doubleWrite;

        private String datasourceType;

        private long datasourceId1;

        private long datasourceId2;

        private List<ColumnConfig> columnConfigs;
    }

    @Data
    public static class ColumnConfig {

        private String columnIndex;

        private String columnKey;

        private String columnHashKey;

        private String columnSortKey;

        private String columnRowKey;

        private String columnFamily;

        private String dataType;
    }

    @Data
    public static class DependentJob {
        private String jobId;
    }

}
