package com.xiaomi.growth.feature.yaml.sqlgen.types;

import lombok.NonNull;

import java.nio.ByteBuffer;

import org.jooq.impl.DefaultDataType;
import org.jooq.impl.SQLDataType;
/**
 * <AUTHOR>
 */
public class BasicDataType extends AbstractDataType {

    @Override
    public DataTypeCategory typeCategory() {
        return DataTypeCategory.BASIC;
    }

    public static class BooleanType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Boolean.class, String.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {
            return SQLDataType.BOOLEAN;
        }
    }

    public static class ByteType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Byte.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {
            return SQLDataType.TINYINT;
        }
    }

    public static class ShortType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Short.class, Byte.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {
            return SQLDataType.SMALLINT;
        }
    }

    public static class IntType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Short.class, Byte.class, Short.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {

            return DefaultDataType.getDefaultDataType("INT");
        }
    }

    public static class LongType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Short.class, Byte.class, Short.class, Integer.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {
            return SQLDataType.BIGINT;
        }
    }

    public static class FloatType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Short.class, Byte.class, Short.class, Integer.class, Float.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {
            return SQLDataType.REAL;
        }
    }

    public static class DoubleType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Short.class, Byte.class, Short.class, Integer.class, Float.class, Double.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {

            return DefaultDataType.getDefaultDataType("double");
        }
    }

    public static class StringType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Short.class, Byte.class, Short.class, Integer.class, Float.class, Double.class, String.class, ByteBuffer.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {

            return DefaultDataType.getDefaultDataType("String");
        }
    }

    public static class BinaryType extends BasicDataType {
        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, ByteBuffer.class, String.class);
        }
        @Override
        public org.jooq.DataType<?> toJooqDataType() {
            return SQLDataType.BINARY;
        }
    }

}
