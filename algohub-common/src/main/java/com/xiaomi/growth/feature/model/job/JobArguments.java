package com.xiaomi.growth.feature.model.job;

import com.xiaomi.growth.feature.constants.EnumConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import picocli.CommandLine.Option;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
public class JobArguments {

    @Option(names = {"-e", "--env"}, required = true)
    private EnumConstant.Env jobEnv;

    @Option(names = {"-t", "--processTime"}, required = true)
    private String processTime;

    @Option(names = {"-c", "--conf"}, description = "-c k1=v1 -c k2=v2")
    private Map<String, String> props = new HashMap<>();

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class FeatureViewExtractJobArguments extends JobArguments {
        @Option(names = {"-vc", "--viewConfig"}, required = true)
        private String viewConfig;

        @Option(names = {"-o", "--output"}, required = true)
        private String output;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BatchViewSyncJobArguments extends JobArguments {
        @Option(names = {"-vc", "--viewConfig"}, required = true)
        private List<String> viewConfig;

        @Option(names = {"-tp", "--type"}, required = true)
        private EnumConstant.DatasourceType type;
    }

    public String getProperty(String key, String defaultValue) {
        return props.getOrDefault(key, defaultValue);
    }

    public String getProperty(String key) {
        return props.get(key);
    }

}

