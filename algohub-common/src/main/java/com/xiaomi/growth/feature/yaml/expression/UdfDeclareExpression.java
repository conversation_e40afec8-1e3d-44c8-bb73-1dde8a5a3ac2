package com.xiaomi.growth.feature.yaml.expression;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class UdfDeclareExpression extends BaseExpression {

    public final static Tag TAG_UDF_DECLARE = new Tag("!udf");
    public final static String FIELD_NAME = "name";
    public final static String FIELD_CLASS = "clazz";

    private String name;
    private String clazz;

    public UdfDeclareExpression(String name, String className) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(className)) {
            throw new IllegalArgumentException("name and class must not be blank");
        }
        this.name = name;
        this.clazz = className;
    }

    public UdfDeclareExpression() {
    }

    public UdfDeclareExpression(Map<String, String> map) {
        this(map.get(FIELD_NAME), map.get(FIELD_CLASS));
    }

    @Override
    public NodeId getNodeId() {
        return NodeId.mapping;
    }

    @Override
    public Tag getTag() {
        return TAG_UDF_DECLARE;
    }

}
