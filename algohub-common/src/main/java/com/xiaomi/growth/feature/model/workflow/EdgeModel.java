package com.xiaomi.growth.feature.model.workflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EdgeModel {
    /**
     * from节点
     */
    private String from;
    /**
     * to节点
     */
    private String to;
    /**
     * 是否偏移, 默认false,使用同周期调度
     */
    private Boolean offset = false;
    /**
     * 触发规则, 默认ALL_SUCCESS,所有上游实例都执行成功了
     */
    private String triggerRule = "ALL_SUCCESS";

    public EdgeModel(String from, String to) {
        this.from = from;
        this.to = to;
    }
}
