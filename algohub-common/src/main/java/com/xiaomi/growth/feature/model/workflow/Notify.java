package com.xiaomi.growth.feature.model.workflow;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Notify {
    // 提供报警能力的平台
    private String notifyProvider = "Falcon";
    // 报警接收对象
    private List<NotifyObject> notifyingReceiver;
    // 超时报警的时间，单位分钟
    private int timeout = 20;
    // 发送通知的条件
    private List<String> notifyIf;
    // 报警级别
    private String notifyLevel;

    public Notify(List<NotifyObject> notifyingReceiver, List<String> notifyIf, String notifyLevel) {
        this.notifyingReceiver = notifyingReceiver;
        this.notifyIf = notifyIf;
        this.notifyLevel = notifyLevel;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotifyObject {
        // 报警方式 NotifyObjectType enum
        private String notifyObjectType;
        // 报警接收对象
        private List<NotifyReceiver> receivers;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotifyReceiver {
        // notifyObjectType为user时，填写用户邮箱前缀
        // notifyObjectType为oncall时，填写oncall报警组的id
        // notifyObjectType为lark时，填写飞书群组的id
        private String id;
        // notifyObjectType为oncall时，填写报警组的名称
        private String value;
    }

    public enum NotifyObjectType {
        ON_CALL("oncall", "oncall群组"),
        LARK("lark", "飞书群组"),
        USER("user", "个人"),
        ;

        @Getter
        private String type;
        @Getter
        private String desc;

        NotifyObjectType(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    public enum NotifyIf {
        SUCCEEDED,
        KILLED,
        FAILED,
        TIMEOUT,
        ;

        NotifyIf() {
        }
    }

    public enum NotifyLevel {
        P0,
        P1,
        P2,
        ;

        NotifyLevel() {
        }
    }

}
