package com.xiaomi.growth.feature.yaml;

import com.xiaomi.growth.feature.yaml.expression.*;
import lombok.NonNull;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.constructor.Construct;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.nodes.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CustomConstructor extends Constructor {

    private final AtomicInteger counter = new AtomicInteger(0);

    private final Map<Tag, Construct> CUSTOM_CONSTRUCTORS = new ConcurrentHashMap<>();

    {
        CUSTOM_CONSTRUCTORS.put(FunctionExpression.TAG_FUNCTION, new ConstructFunction());
        CUSTOM_CONSTRUCTORS.put(SqlBlockExpression.TAG_SQL_BLOCK, new ConstructSqlBlock());
        CUSTOM_CONSTRUCTORS.put(SqlExpression.TAG_SQL_EXP, new ConstructSqlExpression());
        CUSTOM_CONSTRUCTORS.put(PlaceholderExpression.TAG_PLACEHOLDER, new ConstructPlaceholderExpression());
        CUSTOM_CONSTRUCTORS.put(UdfDeclareExpression.TAG_UDF_DECLARE, new ConstructUdf());

    }

    public CustomConstructor() {
        super(new LoaderOptions());
        yamlConstructors.putAll(CUSTOM_CONSTRUCTORS);
    }

    protected class ConstructSqlBlock extends ConstructSequence {
        @Override
        public Object construct(Node node) {
            if (!(node instanceof SequenceNode)) {
                throw new IllegalArgumentException("SqlBlock must be a sequence, but found " + node.getNodeId());
            }

            // 获取函数参数列表
            List<SqlExpression> sqlExpList = new ArrayList<>();
            List<Node> children = ((SequenceNode) node).getValue();
            for (Node child : children) {
                if (child instanceof ScalarNode) {
                    sqlExpList.add(assignId(new SqlExpression(constructScalar((ScalarNode) child))));
                } else {
                    throw new IllegalArgumentException("Invalid SQL block format: " + child.getTag().getValue());
                }
            }

            return assignId(new SqlBlockExpression(sqlExpList));
        }
    }

    protected class ConstructUdf extends ConstructMapping {
        @Override
        public Object construct(Node node) {
            if (!(node instanceof MappingNode)) {
                throw new IllegalArgumentException("UDF declare must be a mapping, but found " + node.getNodeId());
            }
            Map<String, String> udfDeclareMap = ((MappingNode) node).getValue().stream().collect(Collectors.toMap(
                    tup -> ((ScalarNode) (tup.getKeyNode())).getValue(),
                    tup -> ((ScalarNode) (tup.getValueNode())).getValue()));
            return assignId(new UdfDeclareExpression(udfDeclareMap));
        }
    }

    protected class ConstructFunction extends ConstructMapping {
        @Override
        public Object construct(Node node) {
            if (!(node instanceof MappingNode)) {
                throw new IllegalArgumentException("Function definition must be a sequence, but found " + node.getNodeId());
            }
            NodeTuple nodeTuple = ((MappingNode) node).getValue().get(0);
            Node funcNameNode = nodeTuple.getKeyNode();
            Node argsNode = nodeTuple.getValueNode();
            if (!(funcNameNode instanceof ScalarNode)) {
                throw new IllegalArgumentException("Function name must be a scalar");
            }
            if (!(argsNode instanceof SequenceNode)) {
                throw new IllegalArgumentException("Function args must be a sequence");
            }
            // 获取函数名（从锚点中提取）
            String functionName = ((ScalarNode) funcNameNode).getValue();
            // 获取函数参数列表
            List<BaseExpression> args = new ArrayList<>();
            List<Node> children = ((SequenceNode) argsNode).getValue();

            for (Node child : children) {
                Object param = constructObject(child);
                if (isCustomTag(child.getTag())) {
                    args.add(assignId((BaseExpression) param));
                } else {
                    args.add(assignId(new GenericExpression(child, param)));
                }
            }

            return new FunctionExpression(functionName, args.toArray(new BaseExpression[0]));
        }
    }

    protected class ConstructPlaceholderExpression extends ConstructScalar {
        @Override
        public Object construct(Node node) {
            if (!(node instanceof ScalarNode)) {
                throw new IllegalArgumentException("Reference must be a scalar, but found " + node.getNodeId());
            }
            String value = ((ScalarNode) node).getValue();
            return assignId(PlaceholderExpression.identifyExpression(value));
        }
    }

    protected class ConstructSqlExpression extends ConstructScalar {
        @Override
        public Object construct(Node node) {
            if (!(node instanceof ScalarNode)) {
                throw new IllegalArgumentException("SqlExpression must be a scalar, but found " + node.getNodeId());
            }
            String value = ((ScalarNode) node).getValue();
            return assignId(new SqlExpression(value));
        }
    }


    protected boolean isCustomTag(@NonNull Tag tag) {
        return CUSTOM_CONSTRUCTORS.containsKey(tag);
    }


    private <T extends BaseExpression> T assignId(T expression) {
        expression.setGuid(String.valueOf(counter.incrementAndGet()));
        return expression;
    }

}
