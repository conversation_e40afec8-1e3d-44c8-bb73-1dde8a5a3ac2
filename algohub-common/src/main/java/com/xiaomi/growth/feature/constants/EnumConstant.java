package com.xiaomi.growth.feature.constants;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

public class EnumConstant {

    public enum Env {
        /**
         * 正式环境、测试环境、预发环境
         */
        PROD, STAGING, PREVIEW;

        public static final String SYSTEM_ENV = "sys.algohub.environment";

        public static Env of(String env) {
            return Env.valueOf(env.toUpperCase());
        }

        public void setSystemEnv() {
            System.setProperty(SYSTEM_ENV, this.name());
        }
    }

    public enum Domain {
        COMMON("common", "MIUI公共域"),
        MIUI("miui", "MIUI"),
        BROWSER("browser", "浏览器"),
        NEW_HOME("new_home", "内容中心"),
        MIUI_SEARCH("miui_search", "搜索"),
        ECOLOGY("ecology", "生态"),
        APP_STORE("app_store", "应用商店"),
        INSTALLER("installer", "安装器"),
        GAME_SDK("game_sdk", "游戏联运"),
        GAME_CENTER("game_center", "游戏中心"),
        UNION("union", "联盟"),
        PRE_LOAD("pre_load", "预装"),
        POLARIS("polaris", "增长投放"),
        MI_AD("mi_ad", "商业化"),
        MI_VIDEO("mi_video", "小米视频"),
        OTT("ott", "小米电视"),
        DUO_KAN("duo_kan", "多看阅读"),
        MI_AI("mi_ai", "小爱"),
        COMMUNITY("community", "小米社区"),
        MI_SHOP("mi_shop", "小米商城"),
        YOU_PIN("you_pin", "有品"),
        APP_STORE_OVERSEAS("appstore_oversea", "海外应用商店"),
        ;

        @Getter
        private String value;
        @Getter
        private String desc;

        Domain(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static Domain getDomain(String value) {
            for (Domain domain : Domain.values()) {
                if (domain.getValue().equals(value)) {
                    return domain;
                }
            }
            return null;
        }
    }

    public enum UpdateTiming {
        DAY("day", "天级别"),
        HOUR("hour", "小时级别"),
        REAL_TIME("real_time", "实时"),
        ;

        @Getter
        private String value;
        @Getter
        private String desc;

        UpdateTiming(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static UpdateTiming getUpdateTiming(String value) {
            for (UpdateTiming updateTiming : UpdateTiming.values()) {
                if (updateTiming.getValue().equals(value)) {
                    return updateTiming;
                }
            }
            return null;
        }
    }

    public enum FeatureType {
        SINGLE("single", "单值特征"),
        LIST("list", "序列特征"),
        MAP("map", "Map特征"),
        ;

        @Getter
        private String value;
        @Getter
        private String desc;

        FeatureType(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static FeatureType getFeatureType(String value) {
            for (FeatureType featureType : FeatureType.values()) {
                if (featureType.getValue().equals(value)) {
                    return featureType;
                }
            }
            return null;
        }
    }

    public enum FeatureEntity {
        USER("User"),
        ITEM("Item"),
        QUERY("Query"),
        ;

        @Getter
        private String value;

        FeatureEntity(String value) {
            this.value = value;
        }

        public static FeatureEntity getFeatureEntity(String value) {
            for (FeatureEntity featureEntity : FeatureEntity.values()) {
                if (featureEntity.getValue().equals(value)) {
                    return featureEntity;
                }
            }
            return null;
        }
    }

    public enum FeaturePrimaryKey {
        DID("did"),
        GAME_ID("game_id"),
        IMEI_MD5("imei_md5"),
        MID("mid"),
        APP_ID("app_id"),
        PACKAGE_NAME("package_name"),
        CONTENT_ID("content_id"),
        QUERY("query"),
        ;

        @Getter
        private String value;

        FeaturePrimaryKey(String value) {
            this.value = value;
        }

        public static FeaturePrimaryKey getFeaturePrimaryKey(String value) {
            for (FeaturePrimaryKey featurePrimaryKey : FeaturePrimaryKey.values()) {
                if (featurePrimaryKey.getValue().equals(value)) {
                    return featurePrimaryKey;
                }
            }
            return null;
        }
    }

    public enum FeatureValueType {
        INT("int"),
        LONG("long"),
        STRING("string"),
        DOUBLE("double"),
        FLOAT("float"),
        LIST_INT("list<int>"),
        LIST_STRING("list<string>"),
        MAP_STRING_STRING("map<string,string>"),
        MAP_STRING_INT("map<string,int>"),
        MAP_STRING_LONG("map<string,long>"),
        MAP_STRING_FLOAT("map<string,float>"),
        MAP_STRING_DOUBLE("map<string,double>"),
        ;

        @Getter
        private String value;

        FeatureValueType(String value) {
            this.value = value;
        }

        public static FeatureValueType getFeatureValueType(String value) {
            for (FeatureValueType featureValueType : FeatureValueType.values()) {
                if (featureValueType.getValue().equals(value)) {
                    return featureValueType;
                }
            }
            return null;
        }
    }

    public enum FeatureState {
        REGISTERED(1, "已注册"),
        REJECTED(2, "已驳回"),
        APPROVED(3, "已审核"),
        ONLINE(4, "已上线"),
        OFFLINE(5, "已下线"),
        ;

        @Getter
        private Integer value;
        @Getter
        private String desc;

        FeatureState(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static FeatureState getState(Integer value) {
            for (FeatureState featureState : FeatureState.values()) {
                if (featureState.getValue().equals(value)) {
                    return featureState;
                }
            }
            return null;
        }
    }

    public enum ApprovalCode {
        SUBMIT("submit", "审批提交"),
        AGREE("agree", "审批同意"),
        REJECT("reject", "审批驳回"),
        CLOSE("close", "审批关闭"),
        TRANSFER("transfer", "审批转交"),
        DELEGATE("delegate", "审批委派"),
        INTERRUPT("interrupt", "审批终止"),
        ROLLBACK("rollback", "审批回滚"),
        RECALL("recall", "审批撤回"),
        SIGNATURE("signature", "审批加签"),
        SIGN("sign", "审批加签"),
        ;

        @Getter
        private String code;
        @Getter
        private String desc;

        ApprovalCode(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ApprovalCode get(String status) {
            ApprovalCode[] values = ApprovalCode.values();
            for (ApprovalCode value : values) {
                if (value.getCode().equals(status)) {
                    return value;
                }
            }
            return null;
        }

        /**
         * 审批是否终止
         *
         * @param status
         * @return
         */
        public static boolean isApprovalStopped(String status) {
            if (StringUtils.isBlank(status)) {
                return false;
            }
            if (REJECT.code.equals(status) || CLOSE.code.equals(status) || INTERRUPT.code.equals(status)) {
                return true;
            }
            return false;
        }
    }

    public enum FeatureStoreType {
        CLOUD(0, "云侧"),
        CLIENT(1, "端侧"),
        ;

        @Getter
        private int type;
        @Getter
        private String desc;

        FeatureStoreType(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static FeatureStoreType get(int type) {
            FeatureStoreType[] values = FeatureStoreType.values();
            for (FeatureStoreType value : values) {
                if (value.getType() == type) {
                    return value;
                }
            }
            return null;
        }
    }

    public enum FeatureViewType {
        BASE_VIEW(1, "基础视图"),
        COMPLEX_VIEW(2, "复合视图"),
        ;

        @Getter
        private int type;
        @Getter
        private String desc;

        FeatureViewType(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static FeatureViewType get(int type) {
            FeatureViewType[] values = FeatureViewType.values();
            for (FeatureViewType value : values) {
                if (value.getType() == type) {
                    return value;
                }
            }
            return null;
        }
    }

    public enum SyncOnline {
        NO(0, "不落盘"),
        YES(1, "落盘"),
        ;

        @Getter
        private int value;
        @Getter
        private String desc;

        SyncOnline(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static SyncOnline get(int value) {
            SyncOnline[] values = SyncOnline.values();
            for (SyncOnline syncOnline : values) {
                if (syncOnline.getValue() == value) {
                    return syncOnline;
                }
            }
            return null;
        }
    }

    public enum ImportView {
        NO(0, "不导入"),
        YES(1, "导入"),
        ;

        @Getter
        private int value;
        @Getter
        private String desc;

        ImportView(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static ImportView get(int value) {
            ImportView[] values = ImportView.values();
            for (ImportView importView : values) {
                if (importView.getValue() == value) {
                    return importView;
                }
            }
            return null;
        }
    }

    public enum FeatureViewState {
        REGISTERED(1, "初始化"),
        REJECTED(2, "已驳回"),
        APPROVED(3, "已审核"),
        ONLINE(4, "已上线"),
        OFFLINE(5, "已下线"),
        ;

        @Getter
        private int state;
        @Getter
        private String desc;

        FeatureViewState(int state, String desc) {
            this.state = state;
            this.desc = desc;
        }
    }

    public enum UdfState {
        REGISTERED(1, "初始化"),
        REJECTED(2, "已驳回"),
        APPROVED(3, "已审核"),
        ONLINE(4, "已上线"),
        OFFLINE(5, "已下线"),
        ;

        @Getter
        private int state;
        @Getter
        private String desc;

        UdfState(int state, String desc) {
            this.state = state;
            this.desc = desc;
        }
    }

    public enum FeatureViewScheduleState {
        NORMAL(1, "正常调度"),
        STOP(2, "停止调度"),
        ;

        @Getter
        private int state;
        @Getter
        private String desc;

        FeatureViewScheduleState(int state, String desc) {
            this.state = state;
            this.desc = desc;
        }
    }

    public enum OperateType {
        REGISTER(1, "注册"),
        UPDATE(2, "更新"),
        ;

        @Getter
        private int type;
        @Getter
        private String desc;

        OperateType(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    public enum RegisterFrom {
        FORM_REGISTER(1, "表单注册"),
        TABLE_REGISTER(2, "表导入"),
        FILE_REGISTER(3, "文件批量上传"),
        ;

        @Getter
        private int source;
        @Getter
        private String desc;

        RegisterFrom(int source, String desc) {
            this.source = source;
            this.desc = desc;
        }

        public static RegisterFrom get(int value) {
            RegisterFrom[] values = RegisterFrom.values();
            for (RegisterFrom registerFrom : values) {
                if (registerFrom.getSource() == value) {
                    return registerFrom;
                }
            }
            return null;
        }
    }

    public enum DatasourceType {
        PEGASUS("Pegasus", "Pegasus"),
        HBASE("HBase", "HBase"),
        REDIS("Redis", "Redis");

        @Getter
        private String type;
        @Getter
        private String value;

        DatasourceType(String type, String value) {
            this.type = type;
            this.value = value;
        }

        public static DatasourceType get(String type) {
            DatasourceType[] values = DatasourceType.values();
            for (DatasourceType datasourceType : values) {
                if (datasourceType.getType().equals(type)) {
                    return datasourceType;
                }
            }
            return null;
        }
    }

    public enum DatasourceConnectStatus {
        DISCONNECTED(0, "未连接"),
        CONNECTED(1, "已连接"),
        ;

        @Getter
        private int status;
        @Getter
        private String desc;

        DatasourceConnectStatus(int status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public static DatasourceConnectStatus get(int status) {
            DatasourceConnectStatus[] values = DatasourceConnectStatus.values();
            for (DatasourceConnectStatus datasourceConnectStatus : values) {
                if (datasourceConnectStatus.getStatus() == status) {
                    return datasourceConnectStatus;
                }
            }
            return null;
        }
    }

}
