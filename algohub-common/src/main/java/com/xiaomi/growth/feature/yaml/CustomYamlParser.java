package com.xiaomi.growth.feature.yaml;

import org.yaml.snakeyaml.TypeDescription;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.representer.Representer;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CustomYamlParser {

    public static final Yaml DEFAULT_YAML = buildYamlParser();

    public static final Yaml MAP_YAML = buildMapYamlParser();

    public static <T> T parseYaml(String yamlContent, Class<T> clazz) {
        return DEFAULT_YAML.loadAs(yamlContent, clazz);
    }

    public static <T> T parseYamlFromResource(String fileName, Class<T> clazz) {
        return DEFAULT_YAML.loadAs(CustomYamlParser.class.getClassLoader().getResourceAsStream(fileName), clazz);
    }

    public static Yaml buildYamlParser() {
        CustomConstructor constructor = new CustomConstructor();
        CustomResolver resolver = new CustomResolver();
        DumperOptions dumperOptions = new DumperOptions();
        dumperOptions.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        dumperOptions.setPrettyFlow(true);
        Representer representer = new CustomRepresenter(dumperOptions);
        representer.getPropertyUtils().setSkipMissingProperties(true);
        return new Yaml(constructor, representer, dumperOptions, resolver);
    }

    public static Yaml buildMapYamlParser() {
        CustomConstructor constructor = new CustomConstructor();
        // 注册Map类型描述
        TypeDescription mapType = new TypeDescription(Map.class);
        mapType.addPropertyParameters("properties", String.class, Object.class);
        constructor.addTypeDescription(mapType);
        CustomResolver resolver = new CustomResolver();
        DumperOptions dumperOptions = new DumperOptions();
        dumperOptions.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        dumperOptions.setPrettyFlow(true);
        Representer representer = new CustomRepresenter(dumperOptions);
        representer.getPropertyUtils().setSkipMissingProperties(true);
        return new Yaml(constructor, representer, dumperOptions, resolver);
    }

    // 类型安全的Map解析方法
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseToGenericMap(String yamlContent) {
        Object result = MAP_YAML.load(yamlContent);
        if (result instanceof Map) {
            return (Map<String, Object>) result;
        }
        throw new IllegalStateException("YAML content is not a map");
    }
}
