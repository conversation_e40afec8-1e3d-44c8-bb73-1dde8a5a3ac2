package com.xiaomi.growth.feature.model.inapi.rsp;

import lombok.Data;

@Data
public class FeatureInfoRsp {
    private Long id;
    private String domain;
    private String featureEntity;
    private String featurePrimaryKey;
    private String dtPrimaryKey;
    private String featureUpdateTiming;
    private String featureStoreTable;
    // 新增特征对应的表字段名称
    private String featureStoreColumn;
    private int featureStoreType;
    private String featureName;
    private String featureType;
    private String featureValueType;
    private int state;
}
