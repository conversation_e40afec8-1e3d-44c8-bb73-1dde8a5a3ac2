package com.xiaomi.growth.feature.yaml.sqlgen;

import org.apache.commons.lang3.tuple.Pair;
import org.jooq.*;
import org.jooq.conf.RenderKeywordCase;
import org.jooq.conf.RenderNameCase;
import org.jooq.conf.RenderQuotedNames;
import org.jooq.conf.Settings;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;
import org.jooq.impl.SQLDataType;

import java.util.Arrays;
import java.util.stream.Stream;

import static org.jooq.impl.DSL.function;
import static org.jooq.impl.DSL.sql;

/**
 * <AUTHOR>
 */
public class SparkDSL {

    public static final DSLContext SPARK_DSL_CONTEXT = DSL.using(new DefaultConfiguration()
            .set(SQLDialect.MYSQL)
            .set(new Settings()
                    .withRenderFormatted(true)
                    .withRenderKeywordCase(RenderKeywordCase.UPPER)
                    .withRenderSchema(false)
                    .withRenderQuotedNames(RenderQuotedNames.EXPLICIT_DEFAULT_QUOTED)
                    .withRenderNameCase(RenderNameCase.AS_IS)
            ));


    @SafeVarargs
    public static Field<JSONB> map(Pair<Field<String>, Field<?>>... keyValuePairs) {
        Field<?>[] args = Arrays.stream(keyValuePairs)
                .flatMap(p -> Stream.of(p.getLeft(), p.getRight())).toArray(Field[]::new);
        return function("map", SQLDataType.JSONB, args);
    }

    @SafeVarargs
    public static Field<JSONB> named_struct(Pair<Field<String>, Field<?>>... nameValuePairs) {
        Field<?>[] args = Arrays.stream(nameValuePairs)
                .flatMap(p -> Stream.of(p.getLeft(), p.getRight())).toArray(Field[]::new);
        return function("named_struct", SQLDataType.JSONB, args);
    }

    public static Field<?> spark_array(Field<?>... fields) {
        return function("array", SQLDataType.JSON, fields);
    }

    public static SQL create_temp_func(String name, String clazz) {
        return sql(String.format("CREATE TEMPORARY FUNCTION %s AS '%s' ;", name, clazz));
    }


}
