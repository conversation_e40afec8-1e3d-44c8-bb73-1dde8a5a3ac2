package com.xiaomi.growth.feature.yaml.sqlgen;

import com.google.common.collect.Lists;
import com.xiaomi.growth.feature.model.inapi.Result;
import com.xiaomi.growth.feature.model.inapi.rsp.FeatureInfoRsp;
import com.xiaomi.growth.feature.utils.AlgoHubInternalService;
import com.xiaomi.growth.feature.yaml.expression.*;
import com.xiaomi.growth.feature.yaml.sqlgen.types.DataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver.buildFullPath;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class ParsedMappingConfig {

    public final Map<String, Object> originalMapping;

    public final String serializeClassName;

    public final TypeResolver typeResolver;

    private final Map<String, Node> flattenMap = new LinkedHashMap<>();

    private final Map<String, FeatureInfoRsp> featureInfoMap = new HashMap<>();

    private final Map<Tbl, Set<Pair<String, String>>> tableFeaturesMap = new LinkedHashMap<>();

    private final Map<String, Tbl> tableMap = new HashMap<>();

    private final Map<String, DataType> typeMap;

    public ParsedMappingConfig(Map<String, Object> mapping,
                               String serializeClassName,
                               TypeResolver typeResolver) throws ClassNotFoundException {
        this.originalMapping = mapping;
        this.serializeClassName = serializeClassName;
        this.typeResolver = typeResolver;
        flattenMap();
        fillFeatureInfo();
        typeMap = typeResolver.resolve(Class.forName(serializeClassName));
    }

    private void fillFeatureInfo() {
        AlgoHubInternalService service = AlgoHubInternalService.getInstance();
        Result<List<FeatureInfoRsp>> res = service.getFeatureByNames(Lists.newArrayList(featureInfoMap.keySet()));
        Map<String, FeatureInfoRsp> rspMap = res.getData().stream()
                .collect(Collectors.toMap(FeatureInfoRsp::getFeatureName, i -> i, (i1, i2) -> i1));
        featureInfoMap.keySet().forEach(k -> {
            if (!rspMap.containsKey(k)) {
                throw new RuntimeException("特征不存在：" + k);
            }
            FeatureInfoRsp info = rspMap.get(k);
            Tbl tbl = getTblFromFeatureInfo(info);
            Set<Pair<String, String>> features = tableFeaturesMap.getOrDefault(tbl, new HashSet<>());
            // 这里填充需要修改 ，<表字段名,特征名>
            features.add(Pair.of(info.getFeatureStoreColumn(),info.getFeatureName()));
            tableFeaturesMap.put(tbl, features);
            featureInfoMap.put(k, info);
        });
    }

    private Tbl getTblFromFeatureInfo(@NonNull FeatureInfoRsp info) {
        String featureName = info.getFeatureName();
        String table = info.getFeatureStoreTable();
        String pk = info.getDtPrimaryKey();
        if (StringUtils.isBlank(table)) {
            throw new RuntimeException(featureName + " > Feature store table is empty! ");
        }
        Tbl tbl;
        if (tableMap.containsKey(table)) {
            tbl = tableMap.get(table);
        } else {
            tbl = Tbl.builder().guid(table).primaryKey(pk).partitionField(Tbl.DEFAULT_PARTITION_FIELD).build();
            tableMap.put(table, tbl);
        }
        return tbl;
    }

    private void flattenMap() {
        if (this.originalMapping != null) {
            processMap(this.originalMapping, StringUtils.EMPTY, flattenMap);
        }
    }

    @SuppressWarnings("unchecked")
    private void processMap(Map<String, ?> config,
                            String parentPath,
                            @NonNull Map<String, Node> result) {
        for (Map.Entry<String, ?> entry : config.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String fullPath = buildFullPath(parentPath, key);
            if (value instanceof Map) {
                // 递归处理嵌套Map
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                processMap(nestedMap, fullPath, result);
            } else if (value instanceof List) {
                // 递归处理嵌套List
                processList((List<?>) value, key, fullPath, result);
            } else {
                processObject(value, fullPath, key, result);
            }
        }
    }

    private void processObject(Object value, String fullPath, String name, Map<String, Node> result) {
        Node node = Node.builder()
                .name(name)
                .value(value)
                .path(fullPath).build();

        if (value instanceof BaseExpression) {
            collectFeatureRefFromExpression((BaseExpression) value);
        }
        result.put(fullPath, node);
    }

    private void collectFeatureRefFromExpression(BaseExpression expression) {
        if (expression instanceof FeatureReferenceExpression) {
            featureInfoMap.put(((FeatureReferenceExpression) expression).getReference(), null);
        } else if (expression instanceof SqlExpression) {
            ((SqlExpression) expression).extractFeatureRefs().forEach(ref -> featureInfoMap.put(ref, null));
        } else if (expression instanceof SqlBlockExpression) {
            ((SqlBlockExpression) expression).getSqlExpressions().forEach(exp -> exp.extractFeatureRefs().forEach(ref -> featureInfoMap.put(ref, null)));
        } else if (expression instanceof FunctionExpression) {
            Arrays.stream(((FunctionExpression) expression).getArgs()).forEach(this::collectFeatureRefFromExpression);
        }
    }

    @SuppressWarnings("unchecked")
    private void processList(List<?> list,
                             String parentName,
                             String parentPath,
                             Map<String, Node> result) {
        for (int i = 0; i < list.size(); i++) {
            Object element = list.get(i);
            String fullPath = buildListItemFullPath(parentPath, i);
            String itemPath = buildListItemSubPath(parentName, i);
            if (element instanceof Map) {
                // 处理列表中的Map
                processMap((Map<String, Object>) element, fullPath, result);
            } else if (element instanceof List) {
                // 递归处理列表中的List
                processList((List<?>) element, itemPath, fullPath, result);
            } else {
                // 列表中的普通值
                Node node = Node.builder().name(itemPath).value(element).path(fullPath).build();
                result.put(fullPath, node);
            }
        }
    }

    public static String buildListItemFullPath(String parentPath, int inx) {
        return StringUtils.isBlank(parentPath) ? buildListItemSubPath(StringUtils.EMPTY, inx) : parentPath + buildListItemSubPath(parentPath, inx);
    }

    public static String buildListItemSubPath(String parentName, int inx) {
        return String.format("%s[%d]", parentName, inx);
    }

    @Data
    @Builder
    public static class Node {
        private String name;
        private String path;
        private Object value;
    }

    @Data
    @Builder
    public static class Tbl {
        public static final String DEFAULT_PARTITION_FIELD = "date";

        private String guid;
        private String primaryKey;
        private String partitionField;

        public String getTableName() {
            // 替换 横杠 和 第一个点为 下划线
            return guid.replaceFirst("\\.", "_").replaceAll("-", "_");
        }

        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Tbl tbl = (Tbl) o;
            return Objects.equals(guid, tbl.guid);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(guid);
        }
    }

}
