package com.xiaomi.growth.feature.yaml.expression;

import lombok.Getter;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class SqlBlockExpression extends BaseExpression {

    public static final Tag TAG_SQL_BLOCK = new Tag("!sql_block");

    private final List<SqlExpression> sqlExpressions;

    public SqlBlockExpression(List<SqlExpression> sqlExpressions) {
        this.sqlExpressions = sqlExpressions;
    }

    @Override
    public NodeId getNodeId() {
        return NodeId.sequence;
    }

    @Override
    public Tag getTag() {
        return TAG_SQL_BLOCK;
    }

}
