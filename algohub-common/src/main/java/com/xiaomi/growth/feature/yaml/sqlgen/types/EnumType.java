package com.xiaomi.growth.feature.yaml.sqlgen.types;

import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class EnumType extends AbstractDataType {
    private final Class<?> enumClass;

    @Override
    public DataTypeCategory typeCategory() {
        return DataTypeCategory.ENUM;
    }

    public EnumType(@NonNull Class<?> enumClass) {
        this.enumClass = enumClass;
    }

    @Override
    public boolean acceptsObject(@NonNull Object object) {
        return isAssignableFrom(object, String.class, enumClass, Enum.class);
    }

    @Override
    public String toString() {
        return "enum<" + enumClass.getName() + ">";
    }
}
