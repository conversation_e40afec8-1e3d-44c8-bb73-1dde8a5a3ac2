package com.xiaomi.growth.feature.yaml;

import org.yaml.snakeyaml.nodes.Tag;
import org.yaml.snakeyaml.resolver.Resolver;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class CustomResolver extends Resolver {

    /**
     * 特殊表达式正则匹配模式，凡是 ${xxx} 则识别为特殊表达式
     * Placeholder
     */
    public static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{([^}]+)}");

    @Override
    protected void addImplicitResolvers() {
        super.addImplicitResolvers();
        addImplicitResolver(new Tag("!placeholder"), PLACEHOLDER_PATTERN, "${");
    }


}
