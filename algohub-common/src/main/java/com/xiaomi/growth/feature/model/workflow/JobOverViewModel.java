package com.xiaomi.growth.feature.model.workflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobOverViewModel {
    private String id;
    private String workflowId;
    private Integer jobVersion;
    private String jobLastVersion;
    private String jobName;
    private String jobType;
    private Boolean deleted;
    private Position position;

    public JobOverViewModel(String id, String workflowId, Integer jobVersion, String jobName, String jobType, Boolean deleted, Position position) {
        this.id = id;
        this.workflowId = workflowId;
        this.jobVersion = jobVersion;
        this.jobName = jobName;
        this.jobType = jobType;
        this.deleted = deleted;
        this.position = position;
    }
}
