package com.xiaomi.growth.feature.yaml.sqlgen.types;

import lombok.NonNull;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public abstract class AbstractDataType implements DataType {

    @Override
    public String typeName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String toString() {
        return this.typeName();
    }

    @Override
    public boolean acceptsObject(@NonNull Object object) {
        return false;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof DataType)) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        return obj.toString().equals(this.toString());
    }

    protected boolean isAssignableFrom(Object obj, Class<?>... targetClass) {
        if (obj == null) {
            return false;
        }
        return Arrays.stream(targetClass).anyMatch(clazz -> clazz.isAssignableFrom(obj.getClass()));
    }
}
