package com.xiaomi.growth.feature.yaml.sqlgen.types.resolver;

import com.xiaomi.growth.feature.yaml.sqlgen.types.DataType;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TBase;
import org.apache.thrift.TEnum;
import org.apache.thrift.TFieldIdEnum;
import org.apache.thrift.meta_data.*;
import org.apache.thrift.protocol.TType;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import static com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver.buildFullPath;
import static com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver.isThriftClass;

/**
 * <AUTHOR>
 */
public class ThriftTypeResolver implements TypeResolver {

    private static final Map<Class<?>, DataType> THRIFT_CLASS_TYPE_CACHE = new ConcurrentHashMap<>();
    private static final Set<DataType> RESOLED_NESTED_TYPE = ConcurrentHashMap.newKeySet();


    @Override
    public Map<String, DataType> resolve(Class<?> clazz) {
        if (!isThriftClass(clazz)) {
            throw new IllegalArgumentException("Not a thrift class: " + clazz);
        }
        Map<String, DataType> fieldsTypeMap = new LinkedHashMap<>();
        @SuppressWarnings("unchecked") Map<? extends TFieldIdEnum, FieldMetaData> structMetaDataMap = FieldMetaData.getStructMetaDataMap((Class<? extends TBase<?, ?>>) clazz);
        structMetaDataMap.values().forEach(meta -> {
            resolveInternal(StringUtils.EMPTY, meta, fieldsTypeMap);
        });
        return fieldsTypeMap;
    }

    private void resolveInternal(String parentPath, FieldMetaData fieldMetaData, Map<String, DataType> out) {
        String path = buildFullPath(parentPath, fieldMetaData.fieldName);
        DataType dataType = convertDataType(fieldMetaData.valueMetaData);
        out.put(path, dataType);
        switch (fieldMetaData.valueMetaData.type) {
            case TType.STRUCT:
                resolveStruct(path, (StructMetaData) fieldMetaData.valueMetaData, out);
                break;
            case TType.LIST:
            case TType.SET:
            case TType.MAP:
            case TType.ENUM:
                resolveNestedType(dataType, fieldMetaData.valueMetaData, out);
                break;
            default:
                break;
        }

    }

    private DataType tClassIfCached(Class<?> clazz, FieldValueMetaData valueMetaData, Function<FieldValueMetaData, DataType> converter) {
        DataType dataType;
        if (THRIFT_CLASS_TYPE_CACHE.containsKey(clazz)) {
            dataType = THRIFT_CLASS_TYPE_CACHE.get(clazz);
        } else {
            dataType = converter.apply(valueMetaData);
            THRIFT_CLASS_TYPE_CACHE.put(clazz, dataType);
        }
        return dataType;
    }

    private boolean typeResolved(@NonNull DataType dataType) {
        return RESOLED_NESTED_TYPE.contains(dataType);
    }

    private DataType convertDataType(FieldValueMetaData fieldValueMetaData) {
        switch (fieldValueMetaData.type) {
            case TType.BOOL:
                return DataType.BOOLEAN;
            case TType.BYTE:
                return DataType.BYTE;
            case TType.DOUBLE:
                return DataType.DOUBLE;
            case TType.I16:
                return DataType.SHORT;
            case TType.I32:
                return DataType.INT;
            case TType.I64:
                return DataType.LONG;
            case TType.STRING:
                return DataType.STRING;
            case TType.LIST:
                return DataType.createListType(convertDataType(((ListMetaData) fieldValueMetaData).elemMetaData));
            case TType.SET:
                return DataType.createListType(convertDataType(((SetMetaData) fieldValueMetaData).elemMetaData));
            case TType.ENUM:
                Class<? extends TEnum> enumClass = ((EnumMetaData) fieldValueMetaData).enumClass;
                return tClassIfCached(enumClass, fieldValueMetaData, (valueMetaData) -> DataType.createEnumType(enumClass));
            case TType.MAP:
                MapMetaData mapMetaData = (MapMetaData) fieldValueMetaData;
                return DataType.createMapType(convertDataType(mapMetaData.keyMetaData), convertDataType(mapMetaData.valueMetaData));
            case TType.STRUCT:
                Class<? extends TBase> structClass = ((StructMetaData) fieldValueMetaData).structClass;
                return tClassIfCached(structClass, fieldValueMetaData, (valueMetaData) -> {
                    DataType[] structFieldsType = FieldMetaData.getStructMetaDataMap(structClass).values().stream().map(m -> convertDataType(m.valueMetaData)).toArray(DataType[]::new);
                    return DataType.createStructType(structClass, structFieldsType);
                });
            default:
                throw new RuntimeException("Unsupported type: " + fieldValueMetaData.type);
        }
    }

    public void resolveStruct(String parentPath, StructMetaData fieldMetaData, Map<String, DataType> out) {
        FieldMetaData.getStructMetaDataMap(fieldMetaData.structClass).values()
                .forEach(meta -> {
                    resolveInternal(parentPath, meta, out);
                });
    }

    public void resolveNestedType(DataType fieldValueType,
                                  FieldValueMetaData fieldValueMetaData,
                                  Map<String, DataType> out) {
        if (!typeResolved(fieldValueType)) {
            if (fieldValueMetaData instanceof StructMetaData) {
                resolveStruct(fieldValueType.typeName(), (StructMetaData) fieldValueMetaData, out);
            } else if (fieldValueMetaData instanceof MapMetaData) {
                FieldValueMetaData mapValueMetaData = ((MapMetaData) fieldValueMetaData).valueMetaData;
                DataType mapValueDataType = convertDataType(mapValueMetaData);
                out.put(buildFullPath(fieldValueType.typeName(), MAP_VALUE_TYPE_SUFFIX), mapValueDataType);
                resolveNestedType(mapValueDataType, mapValueMetaData, out);
            } else if (fieldValueMetaData instanceof ListMetaData || fieldValueMetaData instanceof SetMetaData) {
                FieldValueMetaData elemValueMetaData = fieldValueMetaData instanceof ListMetaData ?
                        ((ListMetaData) fieldValueMetaData).elemMetaData :
                        ((SetMetaData) fieldValueMetaData).elemMetaData;
                DataType elemDataType = convertDataType(elemValueMetaData);
                out.put(buildFullPath(fieldValueType.typeName(), LIST_ITEM_TYPE_SUFFIX), elemDataType);
                resolveNestedType(elemDataType, elemValueMetaData, out);
            } else if (fieldValueMetaData instanceof EnumMetaData) {
                out.put(fieldValueType.typeName(), DataType.createEnumType(((EnumMetaData) fieldValueMetaData).enumClass));
            }
            RESOLED_NESTED_TYPE.add(fieldValueType);
        }
    }

}
