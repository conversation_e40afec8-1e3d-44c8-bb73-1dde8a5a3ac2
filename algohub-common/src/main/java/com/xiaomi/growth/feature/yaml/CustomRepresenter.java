package com.xiaomi.growth.feature.yaml;

import com.google.common.collect.Maps;
import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import com.xiaomi.growth.feature.yaml.expression.*;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.nodes.Node;
import org.yaml.snakeyaml.nodes.Tag;
import org.yaml.snakeyaml.representer.Representer;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CustomRepresenter extends Representer {

    private final DumperOptions dumperOptions;

    private void addClassTag() {
        this.addClassTag(FunctionExpression.class, FunctionExpression.TAG_FUNCTION);
        this.addClassTag(PlaceholderExpression.class, PlaceholderExpression.TAG_PLACEHOLDER);
        this.addClassTag(SqlBlockExpression.class, SqlBlockExpression.TAG_SQL_BLOCK);
        this.addClassTag(SqlExpression.class, SqlExpression.TAG_SQL_EXP);
        this.addClassTag(UdfDeclareExpression.class, UdfDeclareExpression.TAG_UDF_DECLARE);
        this.addClassTag(FeatureViewConfig.class, Tag.MAP);
    }

    private void addClassRepresent() {
        this.multiRepresenters.put(PlaceholderExpression.class, this::representPlaceholderExpression);
        this.representers.put(FunctionExpression.class, this::representFunctionExpression);
        this.representers.put(GenericExpression.class, this::representGenericExpression);
        this.representers.put(SqlBlockExpression.class, this::representSqlBlockExpression);
        this.representers.put(SqlExpression.class, this::representSqlExpression);
        this.representers.put(UdfDeclareExpression.class, this::representUdfDeclareExpression);
    }

    public CustomRepresenter(DumperOptions options) {
        super(options);
        this.dumperOptions = options;
        addClassTag();
        addClassRepresent();
    }

    private Node representPlaceholderExpression(Object data) {
        if (!(data instanceof PlaceholderExpression)) {
            return null;
        }
        PlaceholderExpression expression = (PlaceholderExpression) data;
        return this.representScalar(expression.getTag(), expression.getOriginExp());
    }

    private Node representGenericExpression(Object data) {
        if (!(data instanceof GenericExpression)) {
            return null;
        }
        GenericExpression expression = (GenericExpression) data;
        return expression.getNode();
    }

    private Node representFunctionExpression(Object data) {
        if (!(data instanceof FunctionExpression)) {
            return null;
        }
        FunctionExpression expression = (FunctionExpression) data;
        Map<String, List<BaseExpression>> map = Maps.newLinkedHashMap();
        map.put(expression.getFunctionName(), Arrays.asList(expression.getArgs()));
        return this.representMapping(expression.getTag(), map, dumperOptions.getDefaultFlowStyle());
    }

    private Node representSqlBlockExpression(Object data) {
        if (!(data instanceof SqlBlockExpression)) {
            return null;
        }
        SqlBlockExpression expression = (SqlBlockExpression) data;
        return this.representSequence(expression.getTag(), expression.getSqlExpressions(), dumperOptions.getDefaultFlowStyle());
    }

    private Node representUdfDeclareExpression(Object data) {
        if (!(data instanceof UdfDeclareExpression)) {
            return null;
        }
        UdfDeclareExpression expression = (UdfDeclareExpression) data;
        Map<String, String> map = Maps.newLinkedHashMap();
        map.put(UdfDeclareExpression.FIELD_NAME, expression.getName());
        map.put(UdfDeclareExpression.FIELD_CLASS, expression.getClazz());
        return this.representMapping(expression.getTag(), map, dumperOptions.getDefaultFlowStyle());
    }

    private Node representSqlExpression(Object data) {
        if (!(data instanceof SqlExpression)) {
            return null;
        }
        SqlExpression expression = (SqlExpression) data;
        return this.representScalar(expression.getTag(), expression.getSqlExp(), DumperOptions.ScalarStyle.LITERAL);
    }

}
