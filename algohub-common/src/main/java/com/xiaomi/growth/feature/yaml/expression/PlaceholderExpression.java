package com.xiaomi.growth.feature.yaml.expression;

import com.xiaomi.growth.feature.yaml.sqlgen.SqlGenerator;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Getter
public class PlaceholderExpression extends BaseExpression {

    public static final Tag TAG_PLACEHOLDER = new Tag("!placeholder");
    /**
     * 识别表达式类型
     */
    private static final Pattern EXPRESSION_TYPE_PATTERN = Pattern.compile("^([a-z]+)\\.(.+)$");

    private final PlaceholderType type;
    private final String originExp;
    private final String reference;

    public static final Map<String, Object> PLACEHOLDER_EXPRESSION_CONST = new ConcurrentHashMap<>();

    static {
        PLACEHOLDER_EXPRESSION_CONST.put("primary_key", SqlGenerator.PRIMARY_KEY_ALIAS);
    }

    @Override
    public NodeId getNodeId() {
        return NodeId.scalar;
    }

    @Override
    public Tag getTag() {
        return TAG_PLACEHOLDER;
    }

    public PlaceholderExpression(String originExp) {
        this.originExp = originExp;
        Pair<PlaceholderType, String> typeAndRef = parseExpression(originExp);
        this.reference = typeAndRef.getRight();
        this.type = typeAndRef.getLeft();
        if (StringUtils.isBlank(reference)) {
            throw new IllegalArgumentException("Invalid expression format: " + originExp + "! reference is blank");
        }
    }

    @Override
    public Object resolvedExpression() {
        return PLACEHOLDER_EXPRESSION_CONST.getOrDefault(reference, reference);
    }

    public static Pair<PlaceholderType, String> parseExpression(String originExp) {
        // 提取表达式内容
        int start = originExp.indexOf("${") + 2;
        int end = originExp.lastIndexOf("}");

        if (start < 2 || end <= start) {
            throw new IllegalArgumentException("Invalid expression format: " + originExp);
        }
        String exp = originExp.substring(start, end);
        Matcher matcher = EXPRESSION_TYPE_PATTERN.matcher(exp);
        if (matcher.matches()) {
            String type = matcher.group(1);
            String reference = matcher.group(2);
            return Pair.of(PlaceholderType.of(type), reference);
        }
        return Pair.of(PlaceholderType.GENERAL, exp);
    }

    public static PlaceholderExpression identifyExpression(String originExp) {
        Pair<PlaceholderType, String> typeAndRef = parseExpression(originExp);
        switch (typeAndRef.getLeft()) {
            case FEATURE:
                return new FeatureReferenceExpression(originExp);
            case FEATURE_VIEW:
                return new ViewReferenceExpression(originExp);
            case ENVIRONMENT:
                return new EnvVarExpression(originExp);
            case GENERAL:
                return new PlaceholderExpression(originExp);
            default:
                throw new IllegalArgumentException("Unsupported reference type: " + typeAndRef.getLeft());
        }
    }

    public static String restorePlaceholder(String originExp, PlaceholderType type) {
        return "${" + type.token + "." + originExp + "}";
    }

    public enum PlaceholderType {

        /**
         * FEATURE feat 特征引用
         * FEATURE_VIEW view 特征视图引用
         * ENVIRONMENT env 系统变量引用
         * GENERAL general 通用引用
         */
        FEATURE("feat"),
        FEATURE_VIEW("view"),
        ENVIRONMENT("env"),
        GENERAL("");

        final String token;

        PlaceholderType(String token) {
            this.token = token;
        }

        public static PlaceholderType of(String token) {
            for (PlaceholderType type : PlaceholderType.values()) {
                if (type.token.equals(token)) {
                    return type;
                }
            }
            // 未匹配到任何类型，返回通用类型
            return GENERAL;
        }

    }


}
