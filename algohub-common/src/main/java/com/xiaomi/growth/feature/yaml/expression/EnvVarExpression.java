package com.xiaomi.growth.feature.yaml.expression;

/**
 * <AUTHOR>
 */
public class EnvVarExpression extends PlaceholderExpression {

    public EnvVarExpression(String originExp) {
        super(originExp);
        if (!PlaceholderType.ENVIRONMENT.equals(this.getType())) {
            throw new IllegalArgumentException("expression type is not match, required: ENVIRONMENT, actual: " + this.getType());
        }
    }


}
