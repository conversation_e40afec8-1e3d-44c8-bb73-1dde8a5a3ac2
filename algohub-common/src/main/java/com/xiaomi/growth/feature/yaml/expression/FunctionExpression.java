package com.xiaomi.growth.feature.yaml.expression;

import lombok.Getter;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

/**
 * <AUTHOR>
 */
@Getter
public class FunctionExpression extends BaseExpression {

    public static final Tag TAG_FUNCTION = new Tag("!func");

    @Override
    public NodeId getNodeId() {
        return NodeId.mapping;
    }

    @Override
    public Tag getTag() {
        return TAG_FUNCTION;
    }

    private final String functionName;
    private final BaseExpression[] args;

    public FunctionExpression(String functionName, BaseExpression[] args) {
        this.functionName = functionName;
        this.args = args;
    }

}
