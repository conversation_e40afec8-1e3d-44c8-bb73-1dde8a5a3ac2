package com.xiaomi.growth.feature.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;


@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER;

    static {
        JsonFactory jsonFactory = JsonFactory.builder()
                .enable(JsonReadFeature.ALLOW_JAVA_COMMENTS)
                .enable(JsonReadFeature.ALLOW_UNQUOTED_FIELD_NAMES)
                .enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS)
                .enable(JsonReadFeature.ALLOW_SINGLE_QUOTES)
                .build();
        OBJECT_MAPPER = new ObjectMapper(jsonFactory);
        SimpleModule module = new SimpleModule();
        OBJECT_MAPPER.registerModule(module);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> String toJson(T t) {
        String result = null;
        try {
            result = OBJECT_MAPPER.writeValueAsString(t);
        } catch (JsonProcessingException e) {
            log.error("toJson failed. value={}", t, e);
        }
        return result;
    }

    public static <T> T fromJson(Object value, Class<T> t) {
        T result = null;
        try {
            result = OBJECT_MAPPER.readValue(value.toString(), t);
        } catch (IOException e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }

    public static <T> T fromJson(Object value, TypeReference<T> ref) {
        T result = null;
        try {
            result = OBJECT_MAPPER.readValue(value.toString(), ref);
        } catch (Exception e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }


    public static <T> List<T> fromJson(Object value, Class<? extends Collection> cls, Class<T> t) {
        List<T> result = null;
        try {
            JavaType type = OBJECT_MAPPER.getTypeFactory().constructCollectionType(cls, t);
            result = OBJECT_MAPPER.readValue(value.toString(), type);
        } catch (IOException e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }

    public static <K, V> Map<K, V> fromJson(Object object, Class<?> cls, Class<K> key, Class<V> value) {
        Map<K, V> result = null;
        try {
            JavaType type = OBJECT_MAPPER.getTypeFactory().constructMapLikeType(cls, key, value);
            result = OBJECT_MAPPER.readValue(object.toString(), type);
        } catch (IOException e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }

    public static <K> Map<K, Object> fromJson2Map(Object object, Class<?> cls, Class<K> key) {
        Map<K, Object> result = null;
        try {
            JavaType type = OBJECT_MAPPER.getTypeFactory().constructMapLikeType(cls, key, Object.class);
            result = OBJECT_MAPPER.readValue(object.toString(), type);
        } catch (IOException e) {
            log.error("fromJson2Map failed. key={}", key, e);
        }
        return result;
    }

}
