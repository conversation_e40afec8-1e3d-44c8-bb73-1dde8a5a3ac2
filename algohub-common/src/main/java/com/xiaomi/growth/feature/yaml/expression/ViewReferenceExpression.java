package com.xiaomi.growth.feature.yaml.expression;

/**
 * <AUTHOR>
 */
public class ViewReferenceExpression extends PlaceholderExpression {

    public ViewReferenceExpression(String originExp) {
        super(originExp);
        if (!PlaceholderType.FEATURE_VIEW.equals(this.getType())) {
            throw new IllegalArgumentException("expression type is not match, required: FEATURE_VIEW, actual: " + this.getType());
        }
    }

}
