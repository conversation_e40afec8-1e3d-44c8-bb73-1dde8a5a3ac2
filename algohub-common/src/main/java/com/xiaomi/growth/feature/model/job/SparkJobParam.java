package com.xiaomi.growth.feature.model.job;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SparkJobParam extends BaseJobParam {
    // 开发语言
    private String devLanguage = "JAVA";
    // spark版本
    String sparkVersion = "3.1";
    // jar包名称
    private String jarName;
    // jarVersion
    private String jarVersion;
    // driverMemory
    private String driverMemory;
    // numExecutors
    private int numExecutors;
    // 是否开启动态调整
    private boolean dynamicAllocationEnabled;
    // 动态调整最小值
    private int dynamicAllocationMinExecutors;
    // 动态调整最大值
    private int dynamicAllocationMaxExecutors;
    // executor内存
    private String executorMemory;
    // 主类
    private String mainClass;
    // 框架参数
    private List<String> frameworkParams;
    // 方法参数
    private List<String> arguments;

    public SparkJobParam(String jobType, String jobName, String owner, String description) {
        super(jobType, jobName, owner, description);
    }

}
