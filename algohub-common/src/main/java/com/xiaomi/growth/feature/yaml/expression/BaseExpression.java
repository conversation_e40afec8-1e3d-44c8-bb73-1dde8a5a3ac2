package com.xiaomi.growth.feature.yaml.expression;

import lombok.Data;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public abstract class BaseExpression implements Serializable {

    private String guid;

    /**
     * 当前表达式的tag
     *
     * @return tag
     */
    public abstract Tag getTag();

    /**
     * 当前表达式的node枚举值
     *
     * @return nodeId
     */
    public abstract NodeId getNodeId();

    public Object resolvedExpression() {
        return null;
    }
}
