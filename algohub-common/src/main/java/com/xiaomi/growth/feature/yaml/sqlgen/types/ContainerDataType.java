package com.xiaomi.growth.feature.yaml.sqlgen.types;

import lombok.Getter;
import lombok.NonNull;

import java.lang.reflect.Array;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ContainerDataType extends AbstractDataType {
    @Override
    public DataTypeCategory typeCategory() {
        return DataTypeCategory.CONTAINER;
    }

    @Override
    public String typeName() {
        return toString();
    }

    @Getter
    public static class ListType<T extends DataType> extends ContainerDataType {
        private final T elementType;

        public ListType(T elementType) {
            this.elementType = elementType;
        }

        @Override
        public String toString() {
            return "list<" + elementType + ">";
        }

        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, List.class, Set.class, Array.class);
        }
    }

    @Getter
    public static class MapType<K, V extends DataType> extends ContainerDataType {
        private final K keyType;
        private final V valueType;

        public MapType(K keyType, V valueType) {
            this.keyType = keyType;
            this.valueType = valueType;
        }

        @Override
        public String toString() {
            return "map<" + keyType + "," + valueType + ">";
        }

        @Override
        public boolean acceptsObject(@NonNull Object object) {
            return isAssignableFrom(object, Map.class);
        }
    }
}
