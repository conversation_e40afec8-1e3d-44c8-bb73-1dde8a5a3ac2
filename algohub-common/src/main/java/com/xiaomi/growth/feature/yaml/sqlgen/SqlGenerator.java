package com.xiaomi.growth.feature.yaml.sqlgen;

import com.google.common.collect.Lists;
import com.xiaomi.growth.feature.yaml.expression.*;
import com.xiaomi.growth.feature.yaml.sqlgen.types.ContainerDataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.DataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.StructType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Select;
import org.jooq.WithStep;
import org.jooq.conf.ParamType;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.xiaomi.growth.feature.yaml.sqlgen.SparkDSL.*;
import static com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver.buildFullPath;
import static org.jooq.impl.DSL.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class SqlGenerator {

    private static final Field<?> NULL_FIELD = field("NULL");

    public static final String PRIMARY_KEY_ALIAS = "id";
    public static final String FEATURE_LIB_ALIAS = "feature_lib";

    public static final String DATE_PARTITION_PLACEHOLDER = "#run_date#";

    public static List<String> generateUdfRefStatement(List<UdfDeclareExpression> udfList) {
        if (udfList == null || udfList.isEmpty()) {
            return Collections.emptyList();
        }
        return udfList.stream().map(udf -> create_temp_func(udf.getName(), udf.getClazz()).toString()).collect(Collectors.toList());
    }

    public static String generateQueryStatement(ParsedMappingConfig config) {
        WithStep featureLibQueryPart = SPARK_DSL_CONTEXT.with(FEATURE_LIB_ALIAS).as(generateFeatureLibSelect(config));
        log.info("generate feature lib query part");
        List<Field<?>> finalFields = generateFinalFields(config, config.getTypeMap());
        log.info("generate final fields");
        return featureLibQueryPart.select(finalFields).from(FEATURE_LIB_ALIAS).getSQL(ParamType.INLINED);
    }

    /**
     * 构造最终查询到字段，会根据制定的序列化类封装成对应的struct
     *
     * @param config  解析后的映射配置
     * @param typeMap 序列化类中每个属性对应的数据类型
     * @return 返回所有的字段
     */
    private static List<Field<?>> generateFinalFields(ParsedMappingConfig config, Map<String, DataType> typeMap) {
        List<Field<?>> finalFields = new LinkedList<>();
        config.getOriginalMapping().forEach((k, v) -> {
            finalFields.add(generateField(v, typeMap, StringUtils.EMPTY, k).as(k));
        });
        return finalFields;
    }

    @SuppressWarnings("unchecked")
    private static Field<?> generateMapField(Map<String, Object> value,
                                             Map<String, DataType> typeMap,
                                             ContainerDataType.MapType<?, ?> fieldType) {
        List<Pair<Field<String>, Field<?>>> keyValuePairs = new LinkedList<>();
        value.forEach((k, v) -> keyValuePairs.add(
                Pair.of(val(k), generateField(v, typeMap, fieldType.typeName(), TypeResolver.MAP_VALUE_TYPE_SUFFIX))));
        return map(keyValuePairs.toArray(new Pair[0]));
    }

    @SuppressWarnings("unchecked")
    private static Field<?> generateStructField(Map<String, Object> value,
                                                Map<String, DataType> typeMap,
                                                String parentPath) {
        List<Pair<Field<String>, Field<?>>> nameValuePairs = new LinkedList<>();
        value.forEach((k, v) -> nameValuePairs.add(Pair.of(val(k), generateField(v, typeMap, parentPath, k))));
        return named_struct(nameValuePairs.toArray(new Pair[0]));
    }

    @SuppressWarnings("unchecked")
    private static Field<?> generateField(Object value, Map<String, DataType> typeMap, String parentPath, String fieldName) {
        if (value == null) {
            return NULL_FIELD;
        }
        String path = buildFullPath(parentPath, fieldName);
        DataType dataType = typeMap.get(path);
        if (!(value instanceof BaseExpression) && !dataType.acceptsObject(value)) {
            throw new RuntimeException("The value class '" + value.getClass() + "' is not accepts type: " + dataType);
        }
        if (value instanceof Map) {
            if (dataType instanceof StructType) {
                return generateStructField((Map<String, Object>) value, typeMap, path);
            } else {
                return generateMapField((Map<String, Object>) value, typeMap, (ContainerDataType.MapType<?, ?>) dataType);
            }
        } else if (value instanceof List) {
            return generateListField((List<Object>) value, typeMap, (ContainerDataType.ListType<?>) dataType);
        } else if (value instanceof BaseExpression) {
            return generateFieldByExpression((BaseExpression) value);
        } else {
            return field(value.toString());
        }
    }

    private static Field<?> generateFieldByExpression(BaseExpression expression) {
        if (expression instanceof SqlExpression) {
            return field(expression.resolvedExpression().toString());
        } else if (expression instanceof SqlBlockExpression) {
            List<Field<?>> fields = new LinkedList<>();
            ((SqlBlockExpression) expression).getSqlExpressions().forEach(exp -> fields.add(generateFieldByExpression(exp)));
            return spark_array(fields.toArray(new Field[0]));
        } else if (expression instanceof EnvVarExpression) {
            return val(expression.resolvedExpression());
        } else if (expression instanceof PlaceholderExpression) {
            return field(expression.resolvedExpression().toString());
        } else {
            return val(expression);
        }
    }

    private static Field<?> generateListField(List<Object> value, Map<String, DataType> typeMap, ContainerDataType.ListType<?> fieldType) {
        List<Field<?>> elements = new LinkedList<>();
        value.forEach(i -> elements.add(generateField(i, typeMap, fieldType.typeName(), TypeResolver.LIST_ITEM_TYPE_SUFFIX)));
        return spark_array(elements.toArray(new Field[0]));
    }

    /**
     * 构造所有用到的特征的查询语句
     *
     * @param config 解析后的映射配置
     * @return 返回select对象
     */
    private static Select<?> generateFeatureLibSelect(ParsedMappingConfig config) {
        Map<ParsedMappingConfig.Tbl, Set<Pair<String, String>>> tableFeaturesMap = config.getTableFeaturesMap();
        if (tableFeaturesMap.isEmpty()) {
            throw new RuntimeException("No table feature found!");
        }
        int total = config.getFeatureInfoMap().size();
        int inx = 0;
        int[] range = IntStream.range(0, total).toArray();
        List<Pair<String, String>> allFeatures = new LinkedList<>();
        Select<Record> select = null;
        for (Map.Entry<ParsedMappingConfig.Tbl, Set<Pair<String, String>>> entry : tableFeaturesMap.entrySet()) {
            ParsedMappingConfig.Tbl table = entry.getKey();
            List<Pair<String, String>> features = Lists.newArrayList(entry.getValue());
            List<Field<?>> featureFields = new LinkedList<>();
            allFeatures.addAll(features);
            featureFields.add(field(table.getPrimaryKey()).as(PRIMARY_KEY_ALIAS));
            for (int i : range) {
                if (i >= inx && i < inx + features.size()) {
                    featureFields.add(field(features.get(i).getLeft()).as(features.get(i).getRight()));
                } else {
                    featureFields.add(NULL_FIELD);
                }
            }
            inx += features.size();
            Select<Record> currentSelect = SPARK_DSL_CONTEXT
                    .select(featureFields)
                    .from(table.getTableName())
                    .where(field(table.getPartitionField()).eq(field(DATE_PARTITION_PLACEHOLDER)));
            select = select == null ? currentSelect : select.unionAll(currentSelect);
        }
        if (tableFeaturesMap.size() == 1) {
            return select;
        }
        // 最外层聚合所有特征
        List<Field<?>> finalFields = allFeatures.stream().map(p -> max(field(p.getRight())).as(p.getRight())).collect(Collectors.toList());
        // 最前面加上主键
        finalFields.add(0, field(PRIMARY_KEY_ALIAS));
        return SPARK_DSL_CONTEXT.select(finalFields).from(select).groupBy(field(PRIMARY_KEY_ALIAS));
    }

}
