package com.xiaomi.growth.feature.yaml.expression;

import lombok.Getter;
import org.yaml.snakeyaml.nodes.Node;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.Tag;

/**
 * <AUTHOR>
 */
@Getter
public class GenericExpression extends BaseExpression {

    private final Node node;
    private final Object value;

    public GenericExpression(Node node, Object value) {
        this.node = node;
        this.value = value;
    }

    @Override
    public Tag getTag() {
        return node.getTag();
    }

    @Override
    public NodeId getNodeId() {
        return node.getNodeId();
    }
}
