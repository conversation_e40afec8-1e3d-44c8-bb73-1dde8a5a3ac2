# template=true       #开启template模式
# feature_view_name=feaview_u_djy_common_d

# template 模式定义变量，通过 #assign 声明

# 声明用到的udf，udf名称不能重复
mappingConfig:
  udf:
    - !udf { name: map_merge_udf, clazz: com.xiaomi.MapMergeUdfFunction }
    - !udf { name: map_merge_udf_v2, clazz: com.xiaomi.MapMergeUdfFunctionV2 }

  mapping:
    did: ${primary_key}
    oaid: ${feat.phone_used_month}
    user_sex: ${feat.user_sex}
    user_age_6_level: ${feat.user_age_6_level}
    user_age_8_level: ${feat.user_age_8_level}
    degree_3_level: ${feat.degree_3_level}
    phone_brand: ${feat.phone_brand}
    phone_series: ${feat.phone_series}
    curr_city_type: ${feat.curr_city_type}
    phone_retail_price: ${feat.phone_retail_price}
    on_office: ${feat.on_office}
    is_commuter: ${feat.is_commuter}
    interest_finance_level: ${feat.interest_finance_level}
    interest_ecom_level: ${feat.interest_ecom_level}
    interest_game_level: ${feat.interest_game_level}
    interest_video_level: ${feat.interest_video_level}
    interest_music_level: ${feat.interest_music_level}
    interest_read_level: ${feat.interest_read_level}
    interest_travel_level: ${feat.interest_travel_level}
    interest_digitaltechnology_level: ${feat.interest_digitaltechnology_level}
    interest_education_level: ${feat.interest_education_level}
    phone_usage_30d_cnt: ${feat.phone_usage_30d_cnt}
    avg_usage_30d: ${feat.avg_usage_30d}
    has_car: ${feat.has_car}
    has_house: ${feat.has_house}
    has_child: ${feat.has_child}
    resident_country: ${feat.resident_country}
    resident_province: ${feat.resident_province}
    resident_city: ${feat.resident_city}
    resident_district: ${feat.resident_district}
    resident_city_type: ${feat.resident_city_type}
    curr_country: ${feat.curr_country}
    curr_province: ${feat.curr_province}
    curr_city: ${feat.curr_city}
    curr_district: ${feat.curr_district}
    occupation: ${feat.curr_district}
    fansInfo:
      star1: ${feat.is_esports_fans}
      star2: ${feat.is_esports_fans}
      star3: ${feat.is_esports_fans}
      star4: ${feat.is_esports_fans}
      star5: ${feat.is_esports_fans}
datasourceConfig:
  - columnConfigs:
      - columnFamily: null
        columnHashKey: pegasusKey1
        columnIndex: ss
        columnKey: null
        columnRowKey: null
        columnSortKey: pegasusSortKey1
        dataType: null
    datasourceId: 1
    datasourceType: Pegasus
  - columnConfigs:
      - columnFamily: columnFamily:column
        columnHashKey: null
        columnIndex: ss
        columnKey: null
        columnRowKey: key
        columnSortKey: null
        dataType: null
    datasourceId: 2
    datasourceType: HBase

schema:
  className: ActionCounter
  mavenInfo:
    artifactId: com.xiaomi.cloud.streaming.platform.workspace15459.UserProfile
    groupId: com.xiaomi.cloud.streaming
    version: THRIFT.1.1748316207
  namespace: com.xiaomi.cloud.streaming.platform.workspace15459
  owner: zhongyuancai
  serializeClassName: com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommon
