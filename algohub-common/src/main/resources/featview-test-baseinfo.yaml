baseInfo:
  baseConfigName: feaview-local-djy-feaview_u_djy_djy_feature_view_9112223_1d-65-base.yml
  createTs: 1750387961000
  creator: zhongyuancai
  dependencyTableName:
    - paimon.tjwqstaging-hdd.paimon_test_ygq.paimon_dac
    - iceberg.zjyprc-hadoop.miuiads.ad_log_v2_lcs
    - iceberg.azamsprc-hadoop.iceberg.ods_iceberg_os_audit_log
  featureEntity: User
  featurePrimaryKey: did
  lifeCycle: 24
  processConfigName: feaview-local-djy-feaview_u_djy_djy_feature_view_9112223_1d-65-process.yml
  projectName: djy
  updateTs: 1750387961000
  version: v1
  viewDesc: xx
  viewId: 65
  viewName: feaview_u_djy_djy_feature_view_9112223_1d
  viewType: 1
jobList:
  - !!com.xiaomi.growth.feature.model.job.SparkJobParam
    arguments:
      - env=local
      - baseInfo=feaview-local-djy-feaview_u_djy_djy_feature_view_9112223_1d-65-base.yml
      - processConfig=feaview-local-djy-feaview_u_djy_djy_feature_view_9112223_1d-65-base.yml
      - processTime=${date-1}
    description: ''
    devLanguage: JAVA
    driverMemory: 4G
    dynamicAllocationEnabled: true
    dynamicAllocationMaxExecutors: 500
    dynamicAllocationMinExecutors: 1
    executorMemory: 3G
    frameworkParams:
      - --conf spark.jars.packages=com.xiaomi.cloud.streaming:com.xiaomi.cloud.streaming.platform.workspace15459.ActionCounter:THRIFT.1.1748316803
    jarName: feature_job
    jarVersion: default
    jobName: FeatureViewExtractJob.feaview_u_djy_djy_feature_view_9112223_1d
    jobType: SPARK_ALPHA
    mainClass: com.xiaomi.growth.feature.job.offline.featview.FeatureViewExtractJob
    mode: SAVE
    numExecutors: 10
    owner: zhongyuancai
    retryIntervals: 30
    retryTimes: 2
    sparkVersion: '3.1'
    workflowId: null