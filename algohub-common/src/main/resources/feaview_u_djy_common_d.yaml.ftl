# template=true       #开启template模式
# feature_view_name=feaview_u_djy_common_d

# template 模式定义变量，通过 #assign 声明
[#assign time_periods = [7,30,90]]

udf: # 声明用到的udf，udf名称不能重复
- !udf { name: map_merge_udf, clazz: com.xiaomi.MapMergeUdfFunction }
- !udf { name: map_merge_udf_v2, clazz: com.xiaomi.MapMergeUdfFunctionV2 }

mapping: # 声明视图结构和特征&视图的映射关系
did: ${env.primaryKey} # did字段为特征主体中的主键，使用系统变量primaryKey声明
oaid: ${feat.u_common_oaid} # oaid字段对应u_common_oaid特征
user_sex: ${feat.u_common_user_sex} # user_sex字段对应特征u_common_user_sex
user_age_6_level: ${feat.u_common_user_age_6_level} # user_age_6_level字段对应特征u_common_user_age_6_level
phone_series: # phone_series需要通过SQL语句对u_common_phone_series进行数值化二次处理
!sql_exp |
case when ${feat.u_common_phone_series} IN ('小米手机','REDMI K系列','小米Mix') then 1
when ${feat.u_common_phone_series} IN ('黑鲨') then 10
when ${feat.u_common_phone_series} IN ('REDMI Note系列') then 2
else -1
end
interest_ecom_level: ${feat.u_common_user_interest_ecom_level}
[#list time_periods as preiod ]  # template模式 通过list指令实现循环遍历数组变量，数组中的元素定义别名为preiod
    avg_usage_duration_[=preiod]d: ${feat.u_common_avg_usage_duration_[=preiod]d}  #[=preiod] 用于声明这里替换为数组元素
[/#list]  # template模式，结束list指令
abtest:
func_min:
- ${feat.u_common_phone_series}
- ${feat.u_common_phone_series}
