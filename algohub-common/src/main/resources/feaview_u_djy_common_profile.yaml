# template=true       #开启template模式
# feature_view_name=feaview_u_djy_common_d

# template 模式定义变量，通过 #assign 声明

# 声明用到的udf，udf名称不能重复
mappingConfig:
  udf:
    - !udf { name: map_merge_udf, clazz: com.xiaomi.MapMergeUdfFunction }
    - !udf { name: map_merge_udf_v2, clazz: com.xiaomi.MapMergeUdfFunctionV2 }

  mapping:
    oaid: ${feat.u_stat_browser_oaid}
    user_sex: ${feat.u_stat_browser_user_sex}
    user_age_6_level: ${feat.u_stat_browser_user_age_6_level}
    user_age_8_level: ${feat.u_stat_browser_user_age_8_level}
    degree_3_level: ${feat.u_stat_browser_degree_3_level}
    phone_brand: ${feat.u_stat_browser_phone_brand}
    phone_series: ${feat.u_stat_browser_phone_series}
    resident_country: ${feat.u_stat_browser_resident_country}
    resident_province: ${feat.u_stat_browser_resident_province}
    resident_city: ${feat.u_stat_browser_resident_city}
    resident_district: ${feat.u_stat_browser_resident_district}
    resident_city_type: ${feat.u_stat_browser_resident_city_type}
    curr_country: ${feat.u_stat_browser_curr_country}
    curr_city: ${feat.u_stat_browser_curr_city}
    curr_district: ${feat.u_stat_browser_curr_district}
    curr_city_type: ${feat.u_stat_browser_curr_city_type}
    phone_retail_price: ${feat.u_stat_browser_phone_retail_price}
    on_office: ${feat.u_stat_browser_on_office}
    is_commuter: ${feat.u_stat_browser_is_commuter}
    interest_finance_level: ${feat.u_stat_browser_interest_finance_level}
    interest_ecom_level: ${feat.u_stat_browser_interest_ecom_level}
    interest_game_level: ${feat.u_stat_browser_interest_game_level}
    interest_video_level: ${feat.u_stat_browser_interest_video_level}
    interest_music_level: ${feat.u_stat_browser_interest_music_level}
    interest_read_level: ${feat.u_stat_browser_interest_read_level}
    interest_travel_level: ${feat.u_stat_browser_interest_travel_level}
    interest_digitaltechnology_level: ${feat.u_stat_browser_interest_digitaltechnology_level}
    interest_education_level: ${feat.u_stat_browser_interest_education_level}
    phone_usage_30d_cnt: ${feat.u_stat_browser_phone_usage_30d_cnt}
    avg_usage_30d: ${feat.u_stat_browser_avg_usage_30d}
    has_car: ${feat.u_stat_browser_has_car}
    has_house: ${feat.u_stat_browser_has_house}
    has_child: ${feat.u_stat_browser_has_child}

    fansInfo: !sql_block
    - !sql_exp >-
      map(
      'k1', ${feat.u_stat_browser_has_house},
      'k2', ${feat.u_stat_browser_has_child},
      'k3', ${feat.u_stat_browser_has_car}
      )
    - !sql_exp >-
      map(
      'k1', ${feat.u_stat_browser_has_house},
      'k2', ${feat.u_stat_browser_has_child},
      'k3', ${feat.u_stat_browser_has_car}
      )

    list_example:
      - ${feat.u_stat_browser_has_car}
      - ${feat.u_stat_browser_has_child}
    curr_province: !sql_exp >-
      
      case when ${feat.u_stat_browser_resident_province} ='西藏自治区' then '西藏'
      
      when ${feat.u_stat_browser_resident_province} ='新疆维吾尔自治区' then '新疆'
      
      when ${feat.u_stat_browser_resident_province} ='广西壮族自治区' then '广西'
      
      when ${feat.u_stat_browser_resident_province} ='宁夏回族自治区' then '宁夏'
      
      when ${feat.u_stat_browser_resident_province} ='内蒙古自治区' then '内蒙古'
      
      else ${feat.u_stat_browser_resident_province}
    fansInfo1:
      star1: ${feat.u_stat_browser_interest_ecom_level}
      star2: ${feat.u_stat_browser_interest_travel_level}
      star3: ${feat.u_stat_browser_interest_travel_level}
      star4: ${feat.u_stat_browser_interest_digitaltechnology_level}
      star5: ${feat.u_stat_browser_interest_education_level}
    test_province:
      provice1:

        provice1_1: ${feat.u_stat_browser_resident_province}

        provice1_2: ${feat.u_stat_browser_resident_province}

      provice2:

        provice2_1: ${feat.u_stat_browser_resident_province}

        provice2_2: ${feat.u_stat_browser_resident_province}

    

datasourceConfig:
  - columnConfigs:
      - columnFamily: null
        columnHashKey: pegasusKey1
        columnIndex: ss
        columnKey: null
        columnRowKey: null
        columnSortKey: pegasusSortKey1
        dataType: null
    datasourceId: 1
    datasourceType: Pegasus
  - columnConfigs:
      - columnFamily: columnFamily:column
        columnHashKey: null
        columnIndex: ss
        columnKey: null
        columnRowKey: key
        columnSortKey: null
        dataType: null
    datasourceId: 2
    datasourceType: HBase

schema:
  className: ActionCounter
  mavenInfo:
    artifactId: com.xiaomi.cloud.streaming.platform.workspace15459.UserProfile
    groupId: com.xiaomi.cloud.streaming
    version: THRIFT.1.1748316207
  namespace: com.xiaomi.cloud.streaming.platform.workspace15459
  owner: zhongyuancai
  serializeClassName: com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommon
