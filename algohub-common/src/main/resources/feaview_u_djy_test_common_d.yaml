# template=true       #开启template模式
# feature_view_name=feaview_u_djy_common_d

# template 模式定义变量，通过 #assign 声明

# 声明用到的udf，udf名称不能重复
mappingConfig:
  udf:
    - !udf { name: map_merge_udf, clazz: com.xiaomi.MapMergeUdfFunction }
    - !udf { name: map_merge_udf_v2, clazz: com.xiaomi.MapMergeUdfFunctionV2 }

  mapping:
    did: ${primary_key}
    oaid: ${feat.placeholder}
    user_sex: ${feat.market_price}
    user_age_6_level: ${feat.mi_staff_type}
    user_age_8_level: ${feat.is_mi_staff}
    phone_brand: ${feat.child_growth_level}
    phone_series: ${feat.placeholder}
    curr_city_type: ${feat.placeholder}
    phone_retail_price: ${feat.placeholder}
    on_office: null
    is_commuter: ${feat.placeholder}
    interest_finance_level: ${feat.placeholder}
    fansInfo: !sql_exp >-
      map(
      'k1', ${feat.placeholder},
      'k2', ${feat.placeholder},
      'k3', ${feat.placeholder}
      )
datasourceConfig:
  - columnConfigs:
      - columnFamily: null
        columnHashKey: pegasusKey1
        columnIndex: ss
        columnKey: null
        columnRowKey: null
        columnSortKey: pegasusSortKey1
        dataType: null
    datasourceId: 1
    datasourceType: Pegasus
  - columnConfigs:
      - columnFamily: columnFamily:column
        columnHashKey: null
        columnIndex: ss
        columnKey: null
        columnRowKey: key
        columnSortKey: null
        dataType: null
    datasourceId: 2
    datasourceType: HBase

schema:
  className: ActionCounter
  mavenInfo:
    artifactId: com.xiaomi.cloud.streaming.platform.workspace15459.UserProfile
    groupId: com.xiaomi.cloud.streaming
    version: THRIFT.1.1748316207
  namespace: com.xiaomi.cloud.streaming.platform.workspace15459
  owner: zhongyuancai
  serializeClassName: com.xiaomi.cloud.streaming.platform.workspace15459.Common
