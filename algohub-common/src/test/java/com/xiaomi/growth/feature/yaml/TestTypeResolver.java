package com.xiaomi.growth.feature.yaml;

import com.google.common.base.Joiner;
import com.xiaomi.cloud.streaming.platform.workspace15459.UserProfile;
import com.xiaomi.growth.feature.yaml.sqlgen.types.DataType;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.ThriftTypeResolver;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.TypeResolver;
import org.junit.Test;

import java.util.Map;

public class TestTypeResolver {

    @Test
    public void testThrift() {
        TypeResolver resolver = new ThriftTypeResolver();
        Map<String, DataType> typeMap = resolver.resolve(UserProfile.class);
        String formatted = Joiner.on("\n").withKeyValueSeparator(": ").join(typeMap);
        System.out.println(formatted);
    }
}
