package com.xiaomi.growth.feature.yaml;

import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import com.xiaomi.growth.feature.yaml.sqlgen.ParsedMappingConfig;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

public class TestYamlParser {

    @Test
    public void parseBaseInfo() throws IOException {
        FeatureViewConfig.BaseInfo baseInfo = CustomYamlParser.parseYamlFromResource("featview-test-baseinfo.yaml", FeatureViewConfig.BaseInfo.class);
        Assert.assertNotNull(baseInfo);
        FeatureViewConfig viewConfig = CustomYamlParser.parseYamlFromResource("feaview_u_djy_test_common_d.yaml", FeatureViewConfig.class);
        Assert.assertNotNull(viewConfig);
        StringWriter out = new StringWriter();
        CustomYamlParser.buildYamlParser().dump(viewConfig, out);
        System.out.println(out);
    }

    @Test
    public void parseMapping() throws IOException {
        FeatureViewConfig viewConfig = CustomYamlParser.parseYamlFromResource("feaview_u_djy_test_common_d.yaml", FeatureViewConfig.class);
        System.out.println(viewConfig);
        Assert.assertNotNull(viewConfig);
        String strDumped = CustomYamlParser.MAP_YAML.dump(viewConfig.getMappingConfig().getMapping());
        Map<String, Object> mapping = CustomYamlParser.parseToGenericMap(strDumped);
        for (Map.Entry<String, Object> entry : mapping.entrySet()) {
            System.out.println(entry.getKey() + ":" + entry.getValue());
        }
        FeatureViewConfig.MappingConfig mappingConfig = new FeatureViewConfig.MappingConfig();
        mappingConfig.setMapping(mapping);
        StringWriter out = new StringWriter();
        CustomYamlParser.MAP_YAML.dump(mappingConfig, out);
        System.out.println(out);
    }
}
