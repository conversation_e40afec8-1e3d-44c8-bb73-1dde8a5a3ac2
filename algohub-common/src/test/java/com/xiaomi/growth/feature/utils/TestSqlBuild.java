package com.xiaomi.growth.feature.utils;

import org.jooq.*;
import org.jooq.conf.*;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;
import org.jooq.SQLDialect;
import org.junit.Test;

import static com.xiaomi.growth.feature.yaml.sqlgen.SparkDSL.create_temp_func;
import static com.xiaomi.growth.feature.yaml.sqlgen.SparkDSL.spark_array;
import static org.jooq.impl.DSL.*;

public class TestSqlBuild {
    // 创建自定义配置
    Configuration configuration = new DefaultConfiguration()
            .set(SQLDialect.MYSQL)
            .set(new Settings()
                    .withRenderFormatted(true)
                    .withRenderKeywordCase(RenderKeywordCase.UPPER)
                    .withRenderSchema(false)
                    .withRenderQuotedNames(RenderQuotedNames.EXPLICIT_DEFAULT_QUOTED)
                    .withRenderNameCase(RenderNameCase.AS_IS)
            );

    // 使用自定义配置创建 DSLContext
    DSLContext dslContext = DSL.using(configuration);

    @Test
    public void testSql() {
        SelectConditionStep<Record1<Object>> select = dslContext.select(field("col1").as("alias")).from(table("table_name").as("a")).where(and(field("t1.id").eq(field("t2.id")), field("t1.id").eq(field("t2.id")), field("t1.id").eq(field("t2.id"))));
        System.out.println(create_temp_func("spark_array", "com.xiaomi.growth.feature.yaml.sqlgen.SparkDSL$SparkArray"));
        System.out.println(dslContext.with("q1").as(select).select(val("a"), spark_array(field("a"), field("b"))).from("q1").getSQL(ParamType.INLINED));

    }

}