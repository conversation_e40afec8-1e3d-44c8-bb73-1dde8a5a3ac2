package com.xiaomi.growth.feature.utils;

import freemarker.template.Template;
import org.junit.Test;

import java.io.File;
import java.io.IOException;

public class TestFreemarker {

    @Test
    public void test() throws IOException {
        FreemarkerEngine engine = new FreemarkerEngine(FreemarkerEngine.CONFIG_SQUARE_BRACKET_SYNTAX);
        Template template = engine.createTemplateFromFile(new File(TestFreemarker.class
                .getClassLoader().getResource("feaview_u_djy_common_d.yaml.ftl").getPath()));
        System.out.println(FreemarkerEngine.processTemplateToString(template, null));
    }
}
