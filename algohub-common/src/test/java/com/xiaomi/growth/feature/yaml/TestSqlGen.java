package com.xiaomi.growth.feature.yaml;

import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import com.xiaomi.growth.feature.model.inapi.rsp.FeatureInfoRsp;
import com.xiaomi.growth.feature.yaml.expression.FeatureReferenceExpression;
import com.xiaomi.growth.feature.yaml.expression.SqlBlockExpression;
import com.xiaomi.growth.feature.yaml.expression.SqlExpression;
import com.xiaomi.growth.feature.yaml.sqlgen.ParsedMappingConfig;
import com.xiaomi.growth.feature.yaml.sqlgen.SqlGeneratorNew;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.ThriftTypeResolver;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonProperty;

public class TestSqlGen {

    // JSON structure classes
    public static class FieldInfo {
        @JsonProperty("viewId")
        public String viewId;

        @JsonProperty("viewName")
        public String viewName;

        @JsonProperty("viewType")
        public int viewType;

        @JsonProperty("path")
        public String path;

        public FieldInfo(String viewId, String viewName, int viewType, String path) {
            this.viewId = viewId;
            this.viewName = viewName;
            this.viewType = viewType;
            this.path = path;
        }
    }

    public static class DependencyColumn {
        @JsonProperty("type")
        public String type;

        @JsonProperty("table")
        public String table;

        @JsonProperty("catalog")
        public String catalog;

        @JsonProperty("database")
        public String database;

        @JsonProperty("column")
        public String column;

        public DependencyColumn(String type, String table, String catalog, String database, String column) {
            this.type = type;
            this.table = table;
            this.catalog = catalog;
            this.database = database;
            this.column = column;
        }
    }

    public static class JsonOutput {
        @JsonProperty("field")
        public List<FieldInfo> field;

        @JsonProperty("dependencyColumns")
        public List<DependencyColumn> dependencyColumns;

        public JsonOutput() {
            this.field = new ArrayList<>();
            this.dependencyColumns = new ArrayList<>();
        }
    }

    @Test
    public void testSqlGen() {
        FeatureViewConfig viewConfig = CustomYamlParser.parseYamlFromResource("feaview_u_djy_common_profile.yaml",
                FeatureViewConfig.class);
        SqlGeneratorNew.generateUdfRefStatement(viewConfig.getMappingConfig().getUdf()).forEach(System.out::println);

        try {
            ParsedMappingConfig parsedMappingConfig = new ParsedMappingConfig(
                    viewConfig.getMappingConfig().getMapping(),
                    viewConfig.getSchema().getSerializeClassName(), new ThriftTypeResolver());

            JsonOutput jsonOutput = new JsonOutput();
            Map<String, ParsedMappingConfig.Node> flattenMap = parsedMappingConfig.getFlattenMap();

            // Add single field info (as shown in your example)
            FieldInfo fieldInfo = new FieldInfo(
                    "U001",
                    "com.xiaomi.feeds.dujiangyan.userProfile",
                    1,
                    "$|com.xiaomi.feeds.dujiangyan.user_feeds_stat_profile|stats['user_stat']");
            jsonOutput.field.add(fieldInfo);

            for (Map.Entry<String, ParsedMappingConfig.Node> entry : flattenMap.entrySet()) {
                String featureName = entry.getKey();
                String path = entry.getValue().getPath();
                Object exp = entry.getValue().getValue();

                if (exp instanceof FeatureReferenceExpression) {
                    FeatureReferenceExpression exp2 = (FeatureReferenceExpression) exp;
                    FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(exp2.getReference());

                    // Parse dependency column info
                    String fullTableName = fis.getFeatureStoreTable();
                    String column = fis.getFeatureStoreColumn();

                    // Parse the table name format:
                    // iceberg.zjyprc-hadoop.browser.dm_browser_did_2_oaid_df
                    String[] parts = fullTableName.split("\\.");
                    if (parts.length >= 4) {
                        String type = parts[0]; // iceberg
                        String catalog = parts[1]; // zjyprc-hadoop -> iceberg_zyjprc_hadoop
                        String database = parts[2]; // browser
                        String table = parts[3]; // dm_browser_did_2_oaid_df

                        // Convert catalog format: zjyprc-hadoop -> iceberg_zyjprc_hadoop
                        String formattedCatalog = type + "_" + catalog.replace("-", "");

                        DependencyColumn depCol = new DependencyColumn(type, table, formattedCatalog, database, column);
                        jsonOutput.dependencyColumns.add(depCol);
                    }
                    
                    // Keep original output for debugging
                    System.out.println("FeatureName:" + featureName + " ," + "  Path:" + path + ",  Expression:"
                            + exp2.getReference());
                    System.out.println(
                            "dependency_column: " + fis.getFeatureStoreTable() + "." + fis.getFeatureStoreColumn());
                    System.out.println();

                } else if (exp instanceof SqlExpression) {
                    SqlExpression exp2 = (SqlExpression) exp;
                    System.out.println("FeatureName:" + featureName + " ," + "  Path:" + path + ",  Expression:"
                            + exp2.extractFeatureRefs());
                } else if (exp instanceof SqlBlockExpression) {
                    SqlBlockExpression exp2 = (SqlBlockExpression) exp;
                    System.out.println("FeatureName:" + featureName + " ," + "  Path:" + path + ",  Expression:"
                            + exp2.getSqlExpressions().stream().map(SqlExpression::extractFeatureRefs)
                                    .collect(Collectors.toList()));
                }
            }

            // Output JSON
            ObjectMapper mapper = new ObjectMapper();
            String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonOutput);
            System.out.println("\n=== JSON Output ===");
            System.out.println(jsonString);

            // System.out.println(SqlGeneratorNew.generateQueryStatement(parsedMappingConfig));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
