<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xiaomi.growth</groupId>
        <artifactId>algohub-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>algohub-common</artifactId>
    <version>0.0.1-SNAPSHOT</version>


    <dependencies>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>thrift</artifactId>
            <version>0.5.0-mdf2.0.9</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cloud.streaming</groupId>
            <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.UserProfile</artifactId>
            <version>THRIFT.1.1748316207</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.data</groupId>
            <artifactId>data-platform-spec-feeds</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cloud.streaming</groupId>
            <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommon</artifactId>
            <version>THRIFT.1.1753081985</version>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.cloud.streaming</groupId>
            <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommonSim</artifactId>
            <version>THRIFT.2.1753175699</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version>
        </dependency>
        <!--        args解析-->
        <dependency>
            <groupId>info.picocli</groupId>
            <artifactId>picocli</artifactId>
            <version>4.7.6</version>
        </dependency>
        <!-- jOOQ核心库 -->
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cloud.streaming</groupId>
            <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.UserProfile</artifactId>
            <version>THRIFT.1.1748316207</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>staging</id>
            <activation>
                <property>
                    <name>staging</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <zk.host>staging</zk.host>
                <spring.profiles.active>staging</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>preview</id>
            <activation>
                <property>
                    <name>preview</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <zk.host>c3_tst</zk.host>
                <spring.profiles.active>preview</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>c3</id>
            <activation>
                <property>
                    <name>c3</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <zk.host>c3</zk.host>
                <spring.profiles.active>c3</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

</project>
