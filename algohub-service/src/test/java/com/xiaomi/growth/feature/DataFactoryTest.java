package com.xiaomi.growth.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xiaomi.growth.feature.datafactory.module.CustomerProperties;
import com.xiaomi.growth.feature.datafactory.module.table.TableInfo;
import com.xiaomi.growth.feature.datafactory.module.table.Type;
import com.xiaomi.growth.feature.datafactory.module.table.req.UpateTableInfoParam;
import com.xiaomi.growth.feature.datafactory.service.IDacService;
import com.xiaomi.growth.feature.datafactory.service.impl.AlphaServiceImpl;
import com.xiaomi.growth.feature.model.job.BaseJobParam;
import com.xiaomi.growth.feature.model.job.SparkJobParam;
import com.xiaomi.growth.feature.model.workflow.WorkflowUpdateType;
import com.xiaomi.growth.feature.model.workflow.req.UpdateWorkflowParam;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowBaseInfo;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowDetail;
import com.xiaomi.growth.feature.service.FeatureServiceImpl;
import com.xiaomi.growth.feature.utils.JsonMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class DataFactoryTest {

    private static final String workflowId = "509897";

    @Autowired
    private FeatureServiceImpl featureService;
    @Autowired
    private AlphaServiceImpl alphaService;
    @Autowired
    private IDacService dacService;
    @Autowired
    private JsonMapper jsonMapper;

    @Test
    public void testPublish() throws Exception {
//        String workflowName = "test workflow is ok";
//        String owner = "zhongyuancai";
//        String quartzCron = "0 0 0 1/1 * ? ";
//
//        List<BaseJobParam> jobList = new ArrayList<>();
//
//        CustomerProperties customerProperties = new CustomerProperties();
//        customerProperties.setSparkJarName("growth_rta_data");
//        List<String> frameworkParams = new ArrayList<>();
//        frameworkParams.add("--conf spark.serializer=\"org.apache.spark.serializer.KryoSerializer\"");
//        frameworkParams.add("--conf spark.yarn.executor.memoryOverhead=\"2g\"");
//        customerProperties.setSparkFrameworkParams(frameworkParams);
//        List<String> arguments = new ArrayList<>();
//        arguments.add("/user/s_lcs/growth/growth_rta_bid_online_feature_all/year=2025/month=${month}/day=${day-2}");
//        customerProperties.setSparkArguments(arguments);
//        customerProperties.setSparkNumExecutors(10);
//        BaseJobParam sparkJob = new SparkJobParam(customerProperties, "default", "testSparkRunner", "xiaomi.online.TestSparkRunner", owner);
//        jobList.add(sparkJob);
//
//        featureService.publish(workflowName, jobList, owner, quartzCron);
    }

    @Test
    public void testPublish1() throws Exception {
        featureService.publish("feaview-staging-djy-feaview_i_djy_publisher_day-68-base.yml");
    }

    @Test
    public void testOnlineAndOffline() throws Exception {
        String workflowId = "509897";
        alphaService.offlineWorkFlow(workflowId);
        WorkFlowBaseInfo baseInfo = alphaService.getWorkFlowBaseInfo(workflowId);
        alphaService.onlineWorkFlow(workflowId);
        baseInfo = alphaService.getWorkFlowBaseInfo(workflowId);
    }


    @Test
    public void testStartAndStopWorkFlow() throws Exception {
        String workflowId = "509897";
        WorkFlowBaseInfo baseInfo = alphaService.getWorkFlowBaseInfo(workflowId);
        alphaService.stopWorkFlow(workflowId);
        baseInfo = alphaService.getWorkFlowBaseInfo(workflowId);
        alphaService.startWorkFlow(workflowId);
        baseInfo = alphaService.getWorkFlowBaseInfo(workflowId);
    }

    @Test
    public void testUpdateWorkFlowVersion() throws Exception {
//        String workflowId = "509897";
//        WorkFlowDetail workFlowDetail = alphaService.getWorkFlowDetail(workflowId);
//
//        UpdateWorkflowParam param = new UpdateWorkflowParam(workFlowDetail.getProjectId(), workFlowDetail.getWorkflowName(), "", new Cron(), workFlowDetail.getQuartzCron(), CronConfigType.CUSTOM.name(), workFlowDetail.getNoticeList(),
//                workFlowDetail.getJobDag(), true, WorkflowUpdateType.INCREASE_VERSION.name(), "");
//        alphaService.updateWorkFlow(String.valueOf(workflowId), param);
    }

    @Test
    public void testGetWorkFlowDetail() throws Exception {
        WorkFlowDetail workFlowDetail = alphaService.getWorkFlowDetail(workflowId);
    }

    @Test
    public void testGetTableDetail() throws Exception {
        String catalog = "iceberg_zjyprc_hadoop";
        String dbName = "tmp";
        String tableName = "feature_test_iceberg";

        TableInfo tableInfo = alphaService.getTableDetail(catalog, dbName, tableName);
        System.out.println(tableInfo);
    }

    @Test
    public void testGetThenUpdate() throws Exception {
        String catalog = "iceberg_zjyprc_hadoop";
        String dbName = "tmp";
        String tableName = "feature_test_iceberg";
        TableInfo tableInfo = alphaService.getTableDetail(catalog, dbName, tableName);

        // update table
        List<TableInfo.Field> fieldList = tableInfo.getFieldList();

        List<UpateTableInfoParam.Column> columns = new ArrayList<>();
        for (TableInfo.Field field : fieldList) {
            UpateTableInfoParam.Column column = new UpateTableInfoParam.Column();
            column.setId(field.getId());
            column.setFieldName(field.getFieldName());
            column.setType(field.getType());
            column.setComment(field.getFieldName());
            columns.add(column);
        }
        UpateTableInfoParam.Column column = new UpateTableInfoParam.Column();
        column.setId(fieldList.stream().mapToInt(TableInfo.Field::getId).max().getAsInt() + 1);
        column.setFieldName("age");
        column.setType(new Type("string", -1, -1));
        column.setComment(column.getFieldName());
        columns.add(column);
        alphaService.updateTable(tableInfo, columns);
    }


    @Test
    public void getTableColumnInfo() throws URISyntaxException, JsonProcessingException {
        dacService.getTableColumnInfo("iceberg.zjyprc-hadoop.miuisearch.dwm_gs_hot_list_di");
    }


}
