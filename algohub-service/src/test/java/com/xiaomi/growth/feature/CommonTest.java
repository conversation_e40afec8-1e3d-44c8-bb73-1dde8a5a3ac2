//package com.xiaomi.growth.feature;
//
//import com.alibaba.cloud.nacos.NacosConfigProperties;
//import com.alibaba.nacos.api.NacosFactory;
//import com.alibaba.nacos.api.config.ConfigService;
//import com.alibaba.nacos.api.config.ConfigType;
//import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaDetailReq;
//import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaListReq;
//import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaDetailVO;
//import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaInfoVO;
//import com.xiaomi.growth.feature.datafactory.service.IAlphaService;
//import com.xiaomi.growth.feature.module.JobConfig;
//import com.xiaomi.growth.feature.module.NacosEnum;
//import com.xiaomi.growth.feature.nacos.CustomNacosConfigManager;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.yaml.snakeyaml.Yaml;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Properties;
//
//@Slf4j
//@SpringBootTest
//@RunWith(SpringJUnit4ClassRunner.class)
//public class CommonTest{
//
//    @Autowired
//    private CustomNacosConfigManager customNacosConfigManager;
//    @Autowired
//    private NacosConfigProperties nacosConfigProperties;
//    @Autowired
//    private IAlphaService alphaService;
//
//    @Test
//    public void testNacosConfigManager() throws InterruptedException {
//        String dataId = "gamecenter-feature-platform-staging-2.json";
//        customNacosConfigManager.publishConfig(dataId, construct());
//        Thread.sleep(1000000000);
//    }
//
//    @Test
//    public void testYamlMergeToNacos() {
//        // 测试代码
//        String text = "spring:\n" +
//                "  profiles:\n" +
//                "    active: staging\n" +
//                "  jackson:\n" +
//                "    time-zone: \"Asia/Shanghai\"\n" +
//                "    date-format: yyyy-MM-dd HH:mm:ss\n" +
//                "  application:\n" +
//                "    name: feature-service";
//        Yaml yaml = new Yaml();
//        Map<String, Object> map = (Map<String, Object>)yaml.load(text);
//        Map<String, Object> resultMap = new HashMap<>();
//        resultMap.put("text-config", map);
//        resultMap.put("base-info", "111");
//        resultMap.put("job-info", "222");
//        String mergeText = yaml.dumpAsMap(resultMap);
//        String dataId = "gamecenter-feature-platform-staging-3.yaml";
//        Properties properties = new Properties();
//        properties.put(NacosEnum.SERVER_ADDR.getValue(), nacosConfigProperties.getServerAddr());
//        properties.put(NacosEnum.USERNAME.getValue(), nacosConfigProperties.getUsername());
//        properties.put(NacosEnum.PASSWORD.getValue(), nacosConfigProperties.getPassword());
//        properties.put(NacosEnum.NAMESPACE.getValue(), nacosConfigProperties.getNamespace());
//        try {
//            ConfigService configService = NacosFactory.createConfigService(properties);
//            configService.publishConfig(dataId, nacosConfigProperties.getGroup(), mergeText, ConfigType.YAML.getType());
//        } catch (Exception e) {
//            log.error("publish config error: {}", e.getMessage());
//        }
//    }
//
//    @Test
//    public void testGetYamlConfigFromNacos() {
//        String dataId = "gamecenter-feature-platform-staging-3.yaml";
//        Properties properties = new Properties();
//        properties.put(NacosEnum.SERVER_ADDR.getValue(), nacosConfigProperties.getServerAddr());
//        properties.put(NacosEnum.USERNAME.getValue(), nacosConfigProperties.getUsername());
//        properties.put(NacosEnum.PASSWORD.getValue(), nacosConfigProperties.getPassword());
//        properties.put(NacosEnum.NAMESPACE.getValue(), nacosConfigProperties.getNamespace());
//        try {
//            ConfigService configService = NacosFactory.createConfigService(properties);
//            String config = configService.getConfig(dataId, nacosConfigProperties.getGroup(), 5000L);
//
//            Yaml yaml = new Yaml();
//            Object load = yaml.load(config);
//            System.out.println(load);
//        } catch (Exception e) {
//            log.error("publish config error: {}", e.getMessage());
//        }
//    }
//
//    @Test
//    public void testQuerySchemaList() throws Exception {
//        QuerySchemaListReq req = new QuerySchemaListReq();
//        req.setPage(1);
//        req.setPageSize(10);
//        List<SchemaInfoVO> list = alphaService.querySchemaList(req);
//        System.out.println(list.size());
//    }
//
//    @Test
//    public void testQuerySchemaDetail() throws Exception {
//        QuerySchemaDetailReq detailReq = new QuerySchemaDetailReq();
//        detailReq.setNamespace("com.xiaomi.cloud.streaming.platform.workspace15459");
//        detailReq.setSchemaName("Test02");
//        SchemaDetailVO detail = alphaService.querySchemaDetail(detailReq);
//        System.out.println(detail);
//    }
//
//
//    private JobConfig construct() {
//        JobConfig jobConfig = new JobConfig();
//        JobConfig.Config config = new JobConfig.Config();
//        JobConfig.SparkConfig sparkConfig = new JobConfig.SparkConfig();
//        sparkConfig.setVersion("3.1");
//        sparkConfig.setNumExecutors(10);
//        config.setSpark(sparkConfig);
//        jobConfig.setConfig(config);
//        return jobConfig;
//    }
//
//}
