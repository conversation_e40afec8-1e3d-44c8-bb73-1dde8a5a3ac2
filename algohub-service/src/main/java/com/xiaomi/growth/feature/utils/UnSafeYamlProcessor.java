package com.xiaomi.growth.feature.utils;

import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Map;

@Component
public class UnSafeYamlProcessor {

    private final Yaml yaml;

    public UnSafeYamlProcessor() {

        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setDefaultScalarStyle(DumperOptions.ScalarStyle.PLAIN);
        options.setExplicitStart(false); // 禁用文档开始标记
        options.setExplicitEnd(false);   // 禁用文档结束标记
        this.yaml = new Yaml(options);
    }

    public Map<String, Object> loadFromResource(String path) {
        InputStream input = getClass().getClassLoader().getResourceAsStream(path);
        return yaml.load(input);
    }

    public Map<String, Object> loadFromString(String yamlStr) {
        return yaml.load(yamlStr);
    }

    public String dump(Object obj) {
        return yaml.dump(obj);
    }

    public String dumpAsMap(Object data) {
        return yaml.dumpAsMap(data);
    }

    public <T> T loadAs(String text, Class<T> type) {
        return yaml.loadAs(text, type);
    }
}