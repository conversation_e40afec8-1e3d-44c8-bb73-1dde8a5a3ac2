package com.xiaomi.growth.feature.datafactory.module.schema.rsp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SchemaDetailInfo schema详细信息, 用于展示单个Schema详情
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchemaDetailVO {
    private BaseSchemaVO baseParams;
    private SpecificSchemaVO specificParams;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseSchemaVO {
        private String className;
        private String namespace;
        private String owner;
        private String createTime;
        // 创建类型: CREATE(本空间创建的，有编辑权限), IMPORT(外部导入的，无编辑权限).
        private String createType;
        private String description;
        private Integer versionCount;
        private List<TableColumn> columns;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpecificSchemaVO {
        private Integer version;
        private String updateUser;
        private String updateTime;
        private String reason;
        private String serializationType;
        private String thriftIdl;
        private MavenCoordinate mavenSnapshotCoordinate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableColumn {
        private String name;
        private String type;
        private String comment;
        private boolean isRequired;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MavenCoordinate {
        private String groupId;
        private String artifactId;
        private String version;
    }

}
