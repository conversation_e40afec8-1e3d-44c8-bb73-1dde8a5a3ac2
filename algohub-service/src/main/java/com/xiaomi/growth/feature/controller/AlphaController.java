package com.xiaomi.growth.feature.controller;

import com.xiaomi.growth.feature.common.model.Result;
import com.xiaomi.growth.feature.model.alpha.req.PublishJobReq;
import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaDetailReq;
import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaListReq;
import com.xiaomi.growth.feature.model.alpha.req.UnPublishJobReq;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaDetailVO;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaInfoVO;
import com.xiaomi.growth.feature.datafactory.module.table.TableInfo;
import com.xiaomi.growth.feature.datafactory.service.IAlphaService;
import com.xiaomi.growth.feature.model.alpha.rsp.PublishJobRsp;
import com.xiaomi.growth.feature.module.req.GetTableInfoReq;
import com.xiaomi.growth.feature.service.IFeatureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/alpha")
@Slf4j
public class AlphaController {

    @Autowired
    private IAlphaService alphaService;
    @Autowired
    private IFeatureService featureService;

    @PostMapping("/table/getBaseInfo")
    public Result<TableInfo> getTableBaseInfo(@RequestBody GetTableInfoReq req) throws Exception {
        TableInfo tableInfo = alphaService.getTableDetail(req.getCatalog(), req.getDbName(), req.getTableName());
        return Result.success(tableInfo);
    }

    @PostMapping("/schema/list")
    public Result<List<SchemaInfoVO>> querySchemaList(@RequestBody QuerySchemaListReq req) throws Exception {
        return Result.success(alphaService.querySchemaList(req));
    }

    @PostMapping("/schema/detail")
    public Result<SchemaDetailVO> querySchemaDetail(@RequestBody QuerySchemaDetailReq req) throws Exception {
        return Result.success(alphaService.querySchemaDetail(req));
    }

    @PostMapping("/featureView/publish")
    public Result<PublishJobRsp> publishFeatureView(@RequestBody PublishJobReq req) throws Exception {
        return Result.success(featureService.publish(req.getConfigName()));
    }

    @PostMapping("/featureView/unPublish")
    public Result<Void> unPublishFeatureView(@RequestBody UnPublishJobReq req) throws Exception {
        featureService.unPublish(req.getWorkflowId());
        return Result.success(null);
    }
}
