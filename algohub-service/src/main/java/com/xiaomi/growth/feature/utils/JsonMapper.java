package com.xiaomi.growth.feature.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class JsonMapper {

    @Autowired
    private ObjectMapper objectMapper;

    public <T> String toJson(T t) {
        String result = null;
        try {
            result = objectMapper.writeValueAsString(t);
        } catch (JsonProcessingException e) {
            log.error("toJson failed. value={}", t, e);
        }
        return result;
    }

    public <T> T fromJson(Object value, Class<T> t) {
        T result = null;
        try {
            result = objectMapper.readValue(value.toString(), t);
        } catch (IOException e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }


    public <T> List<T> fromJson(Object value, Class<? extends Collection> cls, Class<T> t) {
        List<T> result = null;
        try {
            JavaType type = objectMapper.getTypeFactory().constructCollectionType(cls, t);
            result = objectMapper.readValue(value.toString(), type);
        } catch (IOException e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }

    public <K, V> Map<K, V> fromJson(Object object, Class<?> cls, Class<K> key, Class<V> value) {
        Map<K, V> result = null;
        try {
            JavaType type = objectMapper.getTypeFactory().constructMapLikeType(cls, key, value);
            result = objectMapper.readValue(object.toString(), type);
        } catch (IOException e) {
            log.error("fromJson failed. value={}", value, e);
        }
        return result;
    }

    public <K> Map<K, Object> fromJson2Map(Object object, Class<?> cls, Class<K> key) {
        Map<K, Object> result = null;
        try {
            JavaType type = objectMapper.getTypeFactory().constructMapLikeType(cls, key, Object.class);
            result = objectMapper.readValue(object.toString(), type);
        } catch (IOException e) {
            log.error("fromJson2Map failed. key={}", key, e);
        }
        return result;
    }
}
