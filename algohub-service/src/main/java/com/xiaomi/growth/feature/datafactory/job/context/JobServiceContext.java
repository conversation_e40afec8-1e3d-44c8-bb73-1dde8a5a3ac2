package com.xiaomi.growth.feature.datafactory.job.context;

import com.google.common.collect.Maps;
import com.xiaomi.growth.feature.datafactory.job.support.BaseJobService;
import com.xiaomi.growth.feature.model.job.Job;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.PriorityOrdered;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class JobServiceContext<T extends BaseJobService> implements BeanPostProcessor, PriorityOrdered {

    private final Map<String, BaseJobService> jobServiceMap = Maps.newConcurrentMap();

    public BaseJobService getService(String jobType) {
        return jobServiceMap.get(jobType);
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<T> clazz = (Class<T>) bean.getClass();

        if (clazz.isAnnotationPresent(Job.class) && (bean instanceof BaseJobService)) {
            Job annotation = clazz.getAnnotation(Job.class);
            if (annotation != null && annotation.jobType() != null) {
                String jobType = annotation.jobType().name();
                jobServiceMap.put(jobType, (T) bean);
                log.info("put jobServiceMap jobType:{}", jobType);
            }
        }
        return bean;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
