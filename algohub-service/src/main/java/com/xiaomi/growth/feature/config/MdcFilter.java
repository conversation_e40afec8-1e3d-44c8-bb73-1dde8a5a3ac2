package com.xiaomi.growth.feature.config;

import com.xiaomi.growth.feature.utils.UtilConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.util.UUID;

;


@Component
public class MdcFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig){}

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException{
        try{
            MDC.put(UtilConstant.TRACE_ID, UUID.randomUUID().toString().replace("-", ""));
            chain.doFilter(request, response);
        }finally {
            MDC.remove(UtilConstant.TRACE_ID);
        }
    }

    @Override
    public void destroy(){}

    public static String fetchCurrTraceId() {
        return StringUtils.trimToEmpty(MDC.get(UtilConstant.TRACE_ID));
    }

    public static void refreshMDCTraceId() {
        MDC.put(UtilConstant.TRACE_ID, UUID.randomUUID().toString().replace("-", ""));
    }

    public static void  overWriteMDCTraceId(String currTraceId) {
        currTraceId = StringUtils.trimToEmpty(currTraceId);
        MDC.put(UtilConstant.TRACE_ID, currTraceId);
    }

    public static void cleanCurrMDCTraceId() {
        MDC.remove(UtilConstant.TRACE_ID);
    }
}
