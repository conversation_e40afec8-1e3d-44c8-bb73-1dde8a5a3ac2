package com.xiaomi.growth.feature;

import com.xiaomi.keycenter.KeycenterHelper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.ZoneId;
import java.util.TimeZone;


@EnableAsync
@EnableScheduling
@EnableCaching
@EnableTransactionManagement
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xiaomi"})
public class FeatureServiceApplication {
    public static final ZoneId BEIJING_ZONE_ID = ZoneId.of("+8");

    public static void main(String[] args) {
        KeycenterHelper.config("growth_algo_platform_sid");
        TimeZone.setDefault(TimeZone.getTimeZone(BEIJING_ZONE_ID));
        try {
            SpringApplication.run(FeatureServiceApplication.class, args);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}