package com.xiaomi.growth.feature.controller;

import com.xiaomi.growth.feature.common.model.Result;
import com.xiaomi.growth.feature.datafactory.module.table.rsp.DacTableColumnInfo;
import com.xiaomi.growth.feature.datafactory.service.IDacService;
import com.xiaomi.growth.feature.module.req.GetTableInfoReq;
import com.xiaomi.growth.feature.module.req.SearchTableReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/dac")
@Slf4j
public class DacController {

    @Autowired
    private IDacService dacService;

    @PostMapping("/table/columnInfo")
    public Result<DacTableColumnInfo> getTableColumnInfo(@RequestBody GetTableInfoReq req) throws Exception {
        return Result.success(dacService.getTableColumnInfo(req.getGuid()));
    }

    @PostMapping("/table/search")
    public Result<List<String>> searchTableByKeyword(@RequestBody SearchTableReq req) throws Exception {
        return Result.success(dacService.searchTableByKeyword(req));
    }
}
