package com.xiaomi.growth.feature.common.model;

import com.xiaomi.growth.feature.common.constants.Constant;
import com.xiaomi.growth.feature.common.constants.ResultCode;
import lombok.Data;
import org.slf4j.MDC;

import java.io.Serializable;

@Data
public class Result<T> implements Serializable {
    private int code;
    private String message;
    private String traceId;

    private T data;

    Result(int code, String message, T data){
        this.code = code;
        this.message = message;
        this.data = data;
        this.traceId = MDC.get(Constant.TRACE_ID);
    }

    public static <T> Result<T> success(T data) {
        return new Result(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), data);
    }

    public static <T> Result<T> fail(ResultCode code){
        return new Result(code.getCode(), code.getMsg(),(Object) null);
    }

    public static <T> Result<T> fail(int code, String msg){
        return new Result(code, msg,(Object) null);
    }

    public static <T> Result<T> fail(ResultCode retCode, T data){
        return new Result(retCode.getCode(), retCode.getMsg(), data);
    }

    @Override
    public String toString(){
        return String.format("Result{code=%d, msg=%s, data=%s, traceId=%s}", this.code, this.message, this.data, this.traceId);
    }
}
