package com.xiaomi.growth.feature.datafactory.module;

import com.beust.jcommander.internal.Lists;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * 自定义配置
 */
@Data
@RefreshScope
public class CustomerProperties {
    @Value("${spark.version:3.1}")
    private String sparkVersion;
    @Value("${spark.jarName}")
    private String sparkJarName;
    @Value("${spark.driver.memory:10G}")
    private String sparkDriverMemory;
    @Value("${spark.numExecutors:10}")
    private int sparkNumExecutors;
    @Value("${spark.executor.memory:10G}")
    private String sparkExecutorMemory;
    @Value("${spark.dynamicAllocation.enabled:true}")
    private boolean sparkDynamicAllocationEnabled = true;
    @Value("${spark.dynamicAllocation.minExecutors:1}")
    private int sparkDynamicAllocationMinExecutors = 1;
    @Value("${spark.dynamicAllocation.maxExecutors:500}")
    private int sparkDynamicAllocationMaxExecutors = 500;
    @Value("${spark.frameworkParams}")
    private List<String> sparkFrameworkParams = Lists.newArrayList();
    @Value("${spark.arguments}")
    private List<String> sparkArguments = Lists.newArrayList();
}
