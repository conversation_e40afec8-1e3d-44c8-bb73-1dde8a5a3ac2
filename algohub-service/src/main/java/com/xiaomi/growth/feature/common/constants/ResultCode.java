package com.xiaomi.growth.feature.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/11/10 下午2:16
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    SUCCESS(0, "success"),
    UNAUTHORIZED(401, "没有权限！"),
    PRAM_ERROR(400, "参数错误"),
    DATA_FACTORY_ERROR(402, "数据工厂调用错误"),
    UN_PROCESSABLE_ENTITY(422, "请求参数不合法，请仔细检查！"),
    LIMITED(429, "请求超过限制， 请稍后再试！"),
    OTHER(500, "内部异常");
    private int code;
    private String msg;

}
