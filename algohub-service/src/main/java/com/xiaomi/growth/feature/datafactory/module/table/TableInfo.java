package com.xiaomi.growth.feature.datafactory.module.table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableInfo {
    private String service;
    private String name;
    private String catalog;
    private String dbName;
    private String owner;
    private Long createTime;
    private String description;
    private String hdfsUri;
    private List<Field> fieldList;
    private Integer preserveTime;
    private String formatVersion;

    // 嵌套Field类
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Field {
        private Integer id;
        private String fieldName;
        private String fieldType;
        private Type type;
        private String comment;
        private Boolean isKey;
        private Object extra;
    }
}
