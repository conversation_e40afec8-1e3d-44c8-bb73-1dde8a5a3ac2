package com.xiaomi.growth.feature.module;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "job")
public class JobConfig {
    private Config config;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {
        private SparkConfig spark;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SparkConfig {
        private String version;
        private String jarName;
        private Integer numExecutors;
        private DynamicAllocation dynamicAllocation;
        private List<String> frameworkParams;
        private List<String> arguments;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DynamicAllocation {
            private Boolean enabled;
            private Integer minExecutors;
            private Integer maxExecutors;
        }
    }
}
