package com.xiaomi.growth.feature.datafactory.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.growth.feature.common.constants.ResultCode;
import com.xiaomi.growth.feature.config.DacApiConfig;
import com.xiaomi.growth.feature.datafactory.module.table.rsp.DacResponse;
import com.xiaomi.growth.feature.datafactory.module.table.rsp.DacTableColumnInfo;
import com.xiaomi.growth.feature.datafactory.service.IDacService;
import com.xiaomi.growth.feature.exception.BizException;
import com.xiaomi.growth.feature.module.req.SearchTableReq;
import com.xiaomi.growth.feature.utils.HttpUtils;
import com.xiaomi.growth.feature.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DacServiceImpl implements IDacService {

    @Autowired
    private DacApiConfig dacApiConfig;
    @Autowired
    private JsonMapper jsonMapper;
    @Autowired
    private ObjectMapper objectMapper;

    public DacTableColumnInfo getTableColumnInfo(String guid) throws URISyntaxException, JsonProcessingException {
        String url = concatApiUrl(dacApiConfig.getColumnGetUri().replace("${guid}", guid));
        String resp = HttpUtils.get(url, applicationJsonHeader());
        log.info("getTableColumnInfo resp: {}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
        DacResponse<DacTableColumnInfo> response = objectMapper.readValue(resp, new TypeReference<DacResponse<DacTableColumnInfo>>() {
        });
        return response.getData();
    }

    @Override
    public List<String> searchTableByKeyword(SearchTableReq req) throws Exception {
        String url = concatApiUrl(dacApiConfig.getSearchTableUri());
        String resp = HttpUtils.post(url, applicationJsonHeader(), jsonMapper.toJson(req));
        log.info("searchTableByKeyword resp: {}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
        DacResponse<List<String>> response = objectMapper.readValue(resp, new TypeReference<DacResponse<List<String>>>() {
        });
        return response.getData();
    }

    private String concatApiUrl(String uri) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(dacApiConfig.getDomain());
        uriBuilder.setPath(uri);
        return uriBuilder.build().toASCIIString();
    }

    private Map<String, String> applicationJsonHeader() {
        Map<String, String> header = new HashMap<String, String>() {{
            put(HttpUtils.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        }};
        return header;
    }
}
