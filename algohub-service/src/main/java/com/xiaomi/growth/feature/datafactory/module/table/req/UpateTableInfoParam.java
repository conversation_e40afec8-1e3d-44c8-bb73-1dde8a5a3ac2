package com.xiaomi.growth.feature.datafactory.module.table.req;

import com.xiaomi.growth.feature.datafactory.module.table.Type;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpateTableInfoParam {
    private String catalog;
    private String dbName;
    private String name;
    private String owner;
    private Integer preserveTime;
    private List<Column> columnDtos;
    private DatePartition datePartition;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Column {
        private int id;
        private String fieldName;
        private Type type;
        private String comment;
        private String fieldType;
        private boolean isKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatePartition {
        private String field;
        private String datePartitionUnit;
        private String specId;
        private String columnId;
    }

}
