package com.xiaomi.growth.feature.datafactory.utils;

import com.beust.jcommander.internal.Lists;
import com.xiaomi.growth.feature.config.DataFactoryConfig;
import com.xiaomi.growth.feature.model.workflow.Notify;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AlphaUtils {

    @Autowired
    private DataFactoryConfig dataFactoryConfig;

    public List<Notify> getNotifies(String owner) {
        List<Notify> noticeList = Lists.newArrayList();
        List<String> notifyIf = Lists.newArrayList();
        notifyIf.add(Notify.NotifyIf.FAILED.name());

        // 管理员飞书群组报警
        List<Notify.NotifyObject> adminNotifyObjects = Lists.newArrayList();
        adminNotifyObjects.add(new Notify.NotifyObject(Notify.NotifyObjectType.LARK.getType(), Lists.newArrayList(new Notify.NotifyReceiver(dataFactoryConfig.getOncall(), ""))));
        Notify adminNotify = new Notify(adminNotifyObjects, notifyIf, Notify.NotifyLevel.P2.name());
        noticeList.add(adminNotify);

        // 业务负责人报警
        List<Notify.NotifyObject> customerNotifyObjects = Lists.newArrayList();
        customerNotifyObjects.add(new Notify.NotifyObject(Notify.NotifyObjectType.USER.getType(), Lists.newArrayList(new Notify.NotifyReceiver(owner, ""))));
        Notify customerNotify = new Notify(customerNotifyObjects, notifyIf, Notify.NotifyLevel.P2.name());
        noticeList.add(customerNotify);
        return noticeList;
    }
}
