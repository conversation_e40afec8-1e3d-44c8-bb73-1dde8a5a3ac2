package com.xiaomi.growth.feature.datafactory.service;

import com.xiaomi.growth.feature.nacos.JobConfigChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JobConfigReloadListener {

    @Order(1)
    @EventListener(JobConfigChangeEvent.class)
    public void onJobConfigChange(JobConfigChangeEvent event) {
        log.info("job config change event: {}", event);
    }
}
