package com.xiaomi.growth.feature.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "dac.api")
public class DacApiConfig {
    private String domain;
    private String columnGetUri;
    private String searchTableUri;

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getColumnGetUri() {
        return columnGetUri;
    }

    public void setColumnGetUri(String columnGetUri) {
        this.columnGetUri = columnGetUri;
    }

    public String getSearchTableUri() {
        return searchTableUri;
    }

    public void setSearchTableUri(String searchTableUri) {
        this.searchTableUri = searchTableUri;
    }
}