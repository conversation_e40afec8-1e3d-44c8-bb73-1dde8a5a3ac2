package com.xiaomi.growth.feature.exception;

import com.xiaomi.growth.feature.common.constants.ResultCode;
import com.xiaomi.growth.feature.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2024/7/2 上午10:42
 */
@Slf4j
@ControllerAdvice
public class CusExceptionHandler {
    @ExceptionHandler(value = BizException.class)
    @ResponseBody
    public Result<Object> exceptionHandler(BizException e) {
        log.error("Biz异常: ", e);
        return Result.fail(e.getRetCode().getCode(), e.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result<Object> exceptionHandler(Exception e) {
        log.error("全局异常: ", e);
        String msg = e.getMessage();
        return Result.fail(ResultCode.OTHER.getCode(), msg);
    }

}

