package com.xiaomi.growth.feature.service;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.beust.jcommander.internal.Lists;
import com.xiaomi.growth.feature.common.constants.ResultCode;
import com.xiaomi.growth.feature.datafactory.job.context.JobServiceContext;
import com.xiaomi.growth.feature.model.alpha.rsp.PublishJobRsp;
import com.xiaomi.growth.feature.model.workflow.*;
import com.xiaomi.growth.feature.model.workflow.req.UpdateWorkflowParam;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowBaseInfo;
import com.xiaomi.growth.feature.datafactory.service.impl.AlphaServiceImpl;
import com.xiaomi.growth.feature.datafactory.utils.AlphaUtils;
import com.xiaomi.growth.feature.exception.BizException;
import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import com.xiaomi.growth.feature.model.job.BaseJobParam;
import com.xiaomi.growth.feature.module.NacosEnum;
import com.xiaomi.growth.feature.module.rsp.JobDetailRsp;
import com.xiaomi.growth.feature.utils.UnSafeYamlProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

@Slf4j
@Service
public class FeatureServiceImpl implements IFeatureService {

    @Autowired
    private JobServiceContext jobServiceContext;
    @Autowired
    private AlphaServiceImpl alphaService;
    @Autowired
    private AlphaUtils alphaUtils;
    @Autowired
    private NacosConfigProperties nacosConfigProperties;
    @Autowired
    private UnSafeYamlProcessor unSafeYamlProcessor;

    @Override
    public PublishJobRsp publish(String configName) throws Exception {
        ConfigService configService = configService();
        String config = configService.getConfig(configName, nacosConfigProperties.getGroup(), 3000L);
        FeatureViewConfig featureViewConfig = unSafeYamlProcessor.loadAs(config, FeatureViewConfig.class);
        String workflowName = featureViewConfig.getBaseInfo().getViewName();
        String owner = featureViewConfig.getBaseInfo().getCreator();
        String quartzCron = featureViewConfig.getBaseInfo().getQuartzCron();
        List<JobOverViewModel> dependentJobs = getDependentJobs(featureViewConfig.getDependentJobs());

        return publish(workflowName, featureViewConfig.getJobList(), owner, quartzCron, dependentJobs);
    }

    @Override
    public void unPublish(String workflowId) throws Exception {
        alphaService.offlineWorkFlow(workflowId);
    }

    private PublishJobRsp publish(String workflowName,
                                  List<BaseJobParam> jobList,
                                  String owner,
                                  String quartzCron,
                                  List<JobOverViewModel> dependentJobs) throws Exception {
        // 1. 创建工作流
        int workflowId = alphaService.createNewWorkFlow(workflowName);

        // 2. 创建作业
        HashMap<String, JobOverViewModel> nodes = new HashMap<>();
        List<EdgeModel> edges = Lists.newArrayList();
        List<JobOverViewModel> newjobs = new ArrayList<>();

        Integer lastId = 0;
        for (int i = 0; i < jobList.size(); i++) {
            BaseJobParam jobParam = jobList.get(i);
            jobParam.setWorkflowId(String.valueOf(workflowId));
            JobOverViewModel jobOverViewModel = jobServiceContext.getService(jobParam.getJobType()).createJob(jobParam, i);
            lastId = Integer.parseInt(jobOverViewModel.getId());
            nodes.put(jobOverViewModel.getId(), jobOverViewModel);
            newjobs.add(jobOverViewModel);
            if (i > 0) {
                edges.add(new EdgeModel(String.valueOf(lastId), jobOverViewModel.getId()));
            } else {
                for (JobOverViewModel dependentJob : dependentJobs) {
                    nodes.put(dependentJob.getId(), dependentJob);
                    edges.add(new EdgeModel(String.valueOf(dependentJob.getId()), jobOverViewModel.getId()));
                }
            }
        }
        // 3. 添加作业到工作流
        addJobToSchedule(workflowId, nodes, edges, alphaUtils.getNotifies(owner), quartzCron, true);


        return PublishJobRsp.builder()
                .workflowId(String.valueOf(workflowId))
                .jobs(newjobs)
                .build();
    }

    private int addJobToSchedule(int workflowId, HashMap<String, JobOverViewModel> nodes, List<EdgeModel> edges, List<Notify> noticeList, String quartzCron, Boolean enableSchedule) throws Exception {
        WorkFlowBaseInfo baseInfo = alphaService.getWorkFlowBaseInfo(String.valueOf(workflowId));
        UpdateWorkflowParam param = new UpdateWorkflowParam(baseInfo.getProjectId(), baseInfo.getWorkflowName(), "", new Cron(), quartzCron, CronConfigType.CUSTOM.name(), noticeList,
                new JobDagModel(nodes, edges), enableSchedule, WorkflowUpdateType.INCREASE_VERSION.name(), "");
        alphaService.updateWorkFlow(String.valueOf(workflowId), param);
        return baseInfo.getActiveVersion() + 1;
    }

    private ConfigService configService() {
        Properties properties = new Properties();
        properties.put(NacosEnum.SERVER_ADDR.getValue(), nacosConfigProperties.getServerAddr());
        properties.put(NacosEnum.USERNAME.getValue(), nacosConfigProperties.getUsername());
        properties.put(NacosEnum.PASSWORD.getValue(), nacosConfigProperties.getPassword());
        properties.put(NacosEnum.NAMESPACE.getValue(), nacosConfigProperties.getNamespace());
        try {
            return NacosFactory.createConfigService(properties);
        } catch (Exception e) {
            log.error("construct config error: {}", e.getMessage());
            throw new BizException(ResultCode.OTHER);
        }
    }

    private List<JobOverViewModel> getDependentJobs(List<FeatureViewConfig.DependentJob> dependentJobs) throws Exception {
        List<JobOverViewModel> jobOverViewModels = new ArrayList<>();
        if (CollectionUtils.isEmpty(dependentJobs)) {
            return jobOverViewModels;
        }
        for (int i = 0; i < dependentJobs.size(); i++) {
            JobDetailRsp rsp = alphaService.getJobDetail(dependentJobs.get(i).getJobId());
            JobOverViewModel jobOverViewModel = new JobOverViewModel(
                    rsp.getId().toString(),
                    rsp.getWorkflowId().toString(),
                    rsp.getVersion(),
                    rsp.getJobName(),
                    rsp.getJobType(),
                    rsp.getJobDeleted(),
                    new Position("0", String.valueOf(i * 250))
            );
            jobOverViewModels.add(jobOverViewModel);
        }
        return jobOverViewModels;
    }
}
