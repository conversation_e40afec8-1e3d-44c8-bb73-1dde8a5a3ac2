package com.xiaomi.growth.feature.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "data-factory.api")
public class DataFactoryConfig {
    private String token;
    private String domain;
    private String oncall;
    private SparkJarConfig sparkJar;
    private WorkflowConfig workflow;
    private TableConfig table;
    private SchemaConfig schema;

    private JobConfig job;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getOncall() {
        return oncall;
    }

    public void setOncall(String oncall) {
        this.oncall = oncall;
    }

    public SparkJarConfig getSparkJar() {
        return sparkJar;
    }

    public void setSparkJar(SparkJarConfig sparkJar) {
        this.sparkJar = sparkJar;
    }

    public WorkflowConfig getWorkflow() {
        return workflow;
    }

    public void setWorkflow(WorkflowConfig workflow) {
        this.workflow = workflow;
    }

    public TableConfig getTable() {
        return table;
    }

    public void setTable(TableConfig table) {
        this.table = table;
    }

    public SchemaConfig getSchema() {
        return schema;
    }

    public void setSchema(SchemaConfig schema) {
        this.schema = schema;
    }

    public JobConfig getJob() {
        return job;
    }

    public void setJob(JobConfig job) {
        this.job = job;
    }

    @Data
    public static class SparkJarConfig {
        private String createUri;
        private String updateUri;
    }

    @Data
    public static class WorkflowConfig {
        private String createUri;
        private String updateUri;
        private String getBaseUri;
        private String getDetailUri;
        private String onlineUri;
        private String offlineUri;
        private String startUri;
        private String stopUri;
    }

    @Data
    public static class TableConfig {
        private String updateUri;
        private String getUri;
    }

    @Data
    public static class SchemaConfig {
        private String getListUri;
        private String getUri;
    }

    @Data
    public static class JobConfig {
        private String getUri;
    }

}
