package com.xiaomi.growth.feature.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/13 下午5:15
 */
@Configuration
public class WebConfig {

    @Resource
    private MdcFilter customFilter;

    @Bean
    public FilterRegistrationBean<MdcFilter> authorization() {
        FilterRegistrationBean<MdcFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(customFilter);
        registrationBean.addUrlPatterns("/*"); // 设置过滤路径
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE); // 设置过滤器顺序
        return registrationBean;
    }


}
