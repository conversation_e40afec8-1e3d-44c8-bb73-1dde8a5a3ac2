package com.xiaomi.growth.feature.nacos;

import com.xiaomi.growth.feature.module.JobConfig;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class JobConfigChangeEvent extends ApplicationEvent {

    private JobConfig jobConfig;

    public JobConfigChangeEvent(Object source, JobConfig jobConfig) {
        super(source);
        this.jobConfig = jobConfig;
    }
}
