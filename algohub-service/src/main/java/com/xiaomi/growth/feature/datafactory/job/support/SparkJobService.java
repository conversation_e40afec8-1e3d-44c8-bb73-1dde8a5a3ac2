package com.xiaomi.growth.feature.datafactory.job.support;

import com.xiaomi.growth.feature.config.DataFactoryConfig;
import com.xiaomi.growth.feature.model.workflow.JobOverViewModel;
import com.xiaomi.growth.feature.datafactory.service.impl.AlphaServiceImpl;
import com.xiaomi.growth.feature.model.job.BaseJobParam;
import com.xiaomi.growth.feature.model.job.Job;
import com.xiaomi.growth.feature.model.job.JobType;
import com.xiaomi.growth.feature.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@Job(jobType = JobType.SPARK_ALPHA)
public class SparkJobService implements BaseJobService {

    @Autowired
    private DataFactoryConfig dataFactoryConfig;
    @Autowired
    private JsonMapper jsonMapper;
    @Autowired
    private AlphaServiceImpl alphaService;

    @Override
    public JobOverViewModel createJob(BaseJobParam baseJobParam, Integer index) throws Exception {
        return alphaService.createSparkJob(baseJobParam, index);
    }
}
