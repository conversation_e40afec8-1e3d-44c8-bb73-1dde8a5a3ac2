package com.xiaomi.growth.feature.datafactory.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xiaomi.growth.feature.datafactory.module.table.rsp.DacTableColumnInfo;
import com.xiaomi.growth.feature.module.req.SearchTableReq;

import java.net.URISyntaxException;
import java.util.List;

public interface IDacService {

    public DacTableColumnInfo getTableColumnInfo(String guid) throws URISyntaxException, JsonProcessingException;

    public List<String> searchTableByKeyword(SearchTableReq req) throws Exception;
}
