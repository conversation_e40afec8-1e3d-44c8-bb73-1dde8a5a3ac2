package com.xiaomi.growth.feature.datafactory.module.table.rsp;

import lombok.Data;

import java.util.List;

@Data
public class DacTableColumnInfo {
    private int total;
    private List<ColumnInfo> partition;
    private List<ColumnInfo> common;

    @Data
    public static class ColumnInfo {
        private String guid;
        private String tableGuid;
        private Long columnId;
        private String columnName;
        private String columnType;
        private String columnComment;
        private Integer isPartitionKey;
        private Long heat;
        private String columnDesc;
        private String securityLevel;
        private Integer isPrimaryKey;
        private Integer isForeignKey;
    }
}
