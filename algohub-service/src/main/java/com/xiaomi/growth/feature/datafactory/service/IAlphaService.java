package com.xiaomi.growth.feature.datafactory.service;

import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaDetailReq;
import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaListReq;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaDetailVO;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaInfoVO;
import com.xiaomi.growth.feature.datafactory.module.table.TableInfo;
import com.xiaomi.growth.feature.datafactory.module.table.req.UpateTableInfoParam;
import com.xiaomi.growth.feature.model.workflow.JobOverViewModel;
import com.xiaomi.growth.feature.model.workflow.req.UpdateWorkflowParam;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowBaseInfo;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowDetail;
import com.xiaomi.growth.feature.model.job.BaseJobParam;
import com.xiaomi.growth.feature.module.rsp.JobDetailRsp;

import java.util.List;

public interface IAlphaService {

    public JobOverViewModel createSparkJob(BaseJobParam baseJobParam, Integer index) throws Exception;

    public Integer createNewWorkFlow(String workflowName) throws Exception;

    public WorkFlowBaseInfo getWorkFlowBaseInfo(String workflowId) throws Exception;

    public WorkFlowDetail getWorkFlowDetail(String workflowId) throws Exception;

    public WorkFlowDetail getWorkFlowDetail(String workflowId, int version) throws Exception;

    public void updateWorkFlow(String workflowId, UpdateWorkflowParam param) throws Exception;

    public void onlineWorkFlow(String workflowId) throws Exception;

    public void offlineWorkFlow(String workflowId) throws Exception;

    public void startWorkFlow(String workflowId) throws Exception;

    public void stopWorkFlow(String workflowId) throws Exception;

    public TableInfo getTableDetail(String catalog, String dbName, String tableName) throws Exception;

    public void updateTable(TableInfo tableInfo, List<UpateTableInfoParam.Column> columnList) throws Exception;

    public List<SchemaInfoVO> querySchemaList(QuerySchemaListReq req) throws Exception;

    public SchemaDetailVO querySchemaDetail(QuerySchemaDetailReq req) throws Exception;

    public JobDetailRsp getJobDetail(String jobId) throws Exception;
}
