package com.xiaomi.growth.feature.exception;


import com.xiaomi.growth.feature.common.constants.ResultCode;

public class BizException extends RuntimeException {

    private ResultCode retCode;

    public BizException(String msg) {
        super(msg);
        this.retCode = ResultCode.OTHER;
    }
    public BizException(ResultCode retCode) {
        super(retCode.getMsg());
        this.retCode = retCode;
    }

    public BizException(ResultCode retCode, String message) {
        super(message);
        this.retCode = retCode;
    }

    public BizException(ResultCode retCode, Throwable cause) {
        super(retCode.getMsg(), cause);
        this.retCode = retCode;
    }

    public BizException(ResultCode retCode, String message, Throwable cause) {
        super(message, cause);
        this.retCode = retCode;
    }

    public ResultCode getRetCode() {
        return this.retCode;
    }

    public void setRetCode(ResultCode retCode) {
        this.retCode = retCode;
    }
}
