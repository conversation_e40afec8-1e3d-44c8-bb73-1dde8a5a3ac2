package com.xiaomi.growth.feature.datafactory.service.impl;

import com.google.common.collect.Maps;
import com.xiaomi.growth.feature.common.constants.ResultCode;
import com.xiaomi.growth.feature.config.DataFactoryConfig;
import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaDetailReq;
import com.xiaomi.growth.feature.datafactory.module.schema.req.QuerySchemaListReq;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.QuerySchemaListResp;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaDetailVO;
import com.xiaomi.growth.feature.datafactory.module.schema.rsp.SchemaInfoVO;
import com.xiaomi.growth.feature.datafactory.module.table.TableInfo;
import com.xiaomi.growth.feature.datafactory.module.table.req.UpateTableInfoParam;
import com.xiaomi.growth.feature.model.workflow.JobOverViewModel;
import com.xiaomi.growth.feature.model.workflow.Position;
import com.xiaomi.growth.feature.model.workflow.req.UpdateWorkflowParam;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowBaseInfo;
import com.xiaomi.growth.feature.model.workflow.rsp.WorkFlowDetail;
import com.xiaomi.growth.feature.datafactory.service.IAlphaService;
import com.xiaomi.growth.feature.exception.BizException;
import com.xiaomi.growth.feature.model.job.BaseJobParam;
import com.xiaomi.growth.feature.module.rsp.JobDetailRsp;
import com.xiaomi.growth.feature.utils.HttpUtils;
import com.xiaomi.growth.feature.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class AlphaServiceImpl implements IAlphaService {

    @Autowired
    private JsonMapper jsonMapper;
    @Autowired
    private DataFactoryConfig dataFactoryConfig;

    @Override
    public JobOverViewModel createSparkJob(BaseJobParam baseJobParam, Integer index) throws Exception {
        String url = concatApiUrl(dataFactoryConfig.getSparkJar().getCreateUri());
        String jobId = HttpUtils.post(url, applicationJsonHeader(), jsonMapper.toJson(baseJobParam));
        if (StringUtils.isNotEmpty(jobId)) {
            return new JobOverViewModel(jobId, baseJobParam.getWorkflowId(), 1, baseJobParam.getJobName(), baseJobParam.getJobType(), false, new Position(String.valueOf(0), String.valueOf((index + 1) * 100)));
        } else {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public Integer createNewWorkFlow(String workflowName) throws Exception {
        String url = concatApiUrl(dataFactoryConfig.getWorkflow().getCreateUri());
        Map<String, String> body = Maps.newHashMap();
        body.put("workflowName", workflowName);
        String workflowId = HttpUtils.post(url, applicationJsonHeader(), jsonMapper.toJson(body));
        if (StringUtils.isNotEmpty(workflowId)) {
            return Integer.parseInt(workflowId);
        } else {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public WorkFlowBaseInfo getWorkFlowBaseInfo(String workflowId) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getGetBaseUri().replace("{workflowId}", workflowId);
        String url = concatApiUrl(uri);
        String workflowBaseInfo = HttpUtils.get(url, applicationJsonHeader());

        if (StringUtils.isNotEmpty(workflowBaseInfo)) {
            return jsonMapper.fromJson(workflowBaseInfo, WorkFlowBaseInfo.class);
        } else {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public WorkFlowDetail getWorkFlowDetail(String workflowId) throws Exception {
        WorkFlowBaseInfo workFlowBaseInfo = getWorkFlowBaseInfo(workflowId);
        return getWorkFlowDetail(workflowId, workFlowBaseInfo.getActiveVersion());
    }

    @Override
    public WorkFlowDetail getWorkFlowDetail(String workflowId, int version) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getGetDetailUri().replace("{workflowId}", workflowId);
        URIBuilder uriBuilder = new URIBuilder(dataFactoryConfig.getDomain());
        uriBuilder.setPath(uri);
        uriBuilder.setParameter("version", version + "");
        String workflowBaseInfo = HttpUtils.get(uriBuilder.build().toASCIIString(), applicationJsonHeader());
        if (StringUtils.isNotEmpty(workflowBaseInfo)) {
            return jsonMapper.fromJson(workflowBaseInfo, WorkFlowDetail.class);
        } else {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public void updateWorkFlow(String workflowId, UpdateWorkflowParam param) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getUpdateUri().replace("{workflowId}", workflowId);
        String url = concatApiUrl(uri);

        String resp = HttpUtils.put(url, applicationJsonHeader(), jsonMapper.toJson(param));
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public void onlineWorkFlow(String workflowId) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getOnlineUri().replace("{workflowId}", workflowId);
        String url = concatApiUrl(uri);

        WorkFlowBaseInfo baseInfo = getWorkFlowBaseInfo(workflowId);
        Map<String, String> body = new HashMap<String, String>() {{
            // todo check
            put("version", baseInfo.getLastVersion() + "");
            put("auditor", baseInfo.getCreator());
        }};
        String resp = HttpUtils.post(url, applicationJsonHeader(), jsonMapper.toJson(body));
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public void offlineWorkFlow(String workflowId) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getOfflineUri().replace("{workflowId}", workflowId);
        String url = concatApiUrl(uri);
        String resp = HttpUtils.post(url, applicationFormHeader(), null);
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public void startWorkFlow(String workflowId) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getStartUri().replace("{workflowId}", workflowId);
        String url = concatApiUrl(uri);

        WorkFlowBaseInfo baseInfo = getWorkFlowBaseInfo(workflowId);
        Map<String, String> body = new HashMap<String, String>() {{
            // todo check
            put("version", baseInfo.getLastVersion() + "");
        }};

        String resp = HttpUtils.postForm(url, applicationFormHeader(), body);
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public void stopWorkFlow(String workflowId) throws Exception {
        String uri = dataFactoryConfig.getWorkflow().getStopUri().replace("{workflowId}", workflowId);
        String url = concatApiUrl(uri);

        WorkFlowBaseInfo baseInfo = getWorkFlowBaseInfo(workflowId);
        Map<String, String> body = new HashMap<String, String>() {{
            // todo check
            put("version", baseInfo.getLastVersion() + "");
        }};

        String resp = HttpUtils.postForm(url, applicationFormHeader(), body);
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    @Override
    public TableInfo getTableDetail(String catalog, String dbName, String tableName) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(dataFactoryConfig.getDomain());
        uriBuilder.setPath(dataFactoryConfig.getTable().getGetUri());
        uriBuilder.setParameter("catalog", catalog);
        uriBuilder.setParameter("dbName", dbName);
        uriBuilder.setParameter("tableNameEn", tableName);

        String resp = HttpUtils.get(uriBuilder.build().toASCIIString(), applicationJsonHeader());
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
        return jsonMapper.fromJson(resp, TableInfo.class);
    }

    @Override
    public void updateTable(TableInfo tableInfo, List<UpateTableInfoParam.Column> columnList) throws Exception {
        String url = concatApiUrl(dataFactoryConfig.getTable().getUpdateUri());

        UpateTableInfoParam param = new UpateTableInfoParam();
        param.setCatalog(tableInfo.getCatalog());
        param.setDbName(tableInfo.getDbName());
        param.setName(tableInfo.getName());
        param.setColumnDtos(columnList);
        param.setOwner(tableInfo.getOwner());
        param.setPreserveTime(tableInfo.getPreserveTime());

        param.setDatePartition(new UpateTableInfoParam.DatePartition());
        String resp = HttpUtils.post(url, applicationJsonHeader(), jsonMapper.toJson(param));
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
        // 解析code
    }

    @Override
    public List<SchemaInfoVO> querySchemaList(QuerySchemaListReq req) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(dataFactoryConfig.getDomain());
        uriBuilder.setPath(dataFactoryConfig.getSchema().getGetListUri());
        uriBuilder.setParameter("page", req.getPage() + "");
        uriBuilder.setParameter("pageSize", req.getPageSize() + "");
        if (StringUtils.isNotEmpty(req.getKeyword())) {
            uriBuilder.setParameter("keyword", req.getKeyword());
        }
        if (StringUtils.isNotEmpty(req.getOwner())) {
            uriBuilder.setParameter("owner", req.getOwner());
        }
        String resp = HttpUtils.get(uriBuilder.build().toASCIIString(), applicationFormHeader());
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
        QuerySchemaListResp schemaListResp = jsonMapper.fromJson(resp, QuerySchemaListResp.class);
        return Objects.nonNull(schemaListResp) ? schemaListResp.getData() : Collections.emptyList();
    }

    @Override
    public SchemaDetailVO querySchemaDetail(QuerySchemaDetailReq req) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(dataFactoryConfig.getDomain());
        uriBuilder.setPath(dataFactoryConfig.getSchema().getGetUri());
        uriBuilder.setParameter("schemaName", req.getNamespace() + "." + req.getSchemaName());
        if (Objects.nonNull(req.getVersion())) {
            uriBuilder.setParameter("version", req.getVersion() + "");
        }
        String resp = HttpUtils.get(uriBuilder.build().toASCIIString(), applicationFormHeader());
        if (StringUtils.isEmpty(resp)) {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
        return jsonMapper.fromJson(resp, SchemaDetailVO.class);
    }

    @Override
    public JobDetailRsp getJobDetail(String jobId) throws Exception {
        String uri = dataFactoryConfig.getJob().getGetUri().replace("{jobId}", jobId);
        String url = concatApiUrl(uri);
        String rsp = HttpUtils.get(url, applicationJsonHeader());

        if (StringUtils.isNotEmpty(rsp)) {
            return jsonMapper.fromJson(rsp, JobDetailRsp.class);
        } else {
            throw new BizException(ResultCode.DATA_FACTORY_ERROR);
        }
    }

    private Map<String, String> applicationJsonHeader() {
        Map<String, String> header = new HashMap<String, String>() {{
            put(HttpUtils.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
            put(HttpUtils.AUTH, "workspace-token/1.0 " + dataFactoryConfig.getToken());
        }};
        return header;
    }

    private Map<String, String> applicationFormHeader() {
        Map<String, String> header = new HashMap<String, String>() {{
            put(HttpUtils.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            put(HttpUtils.AUTH, "workspace-token/1.0 " + dataFactoryConfig.getToken());
        }};
        return header;
    }

    private String concatApiUrl(String uri) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(dataFactoryConfig.getDomain());
        uriBuilder.setPath(uri);
        return uriBuilder.build().toASCIIString();
    }
}
