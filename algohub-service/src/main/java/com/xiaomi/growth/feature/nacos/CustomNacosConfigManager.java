package com.xiaomi.growth.feature.nacos;


import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.xiaomi.growth.feature.module.JobConfig;
import com.xiaomi.growth.feature.module.NacosEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Properties;
import java.util.concurrent.Executor;

@Slf4j
@Service
public class CustomNacosConfigManager<T> implements InitializingBean, DisposableBean {
    private ConfigService configService;
    private ObjectMapper mapper;

    @Autowired
    private NacosConfigProperties nacosConfigProperties;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 格式化输出
        mapper = new ObjectMapper();
        mapper.enable(SerializationFeature.INDENT_OUTPUT);

        // nacos config
        Properties properties = new Properties();
        properties.put(NacosEnum.SERVER_ADDR.getValue(), nacosConfigProperties.getServerAddr());
        properties.put(NacosEnum.USERNAME.getValue(), nacosConfigProperties.getUsername());
        properties.put(NacosEnum.PASSWORD.getValue(), nacosConfigProperties.getPassword());
        properties.put(NacosEnum.NAMESPACE.getValue(), nacosConfigProperties.getNamespace());
        try {
            configService = NacosFactory.createConfigService(properties);

            // configService 初始化成功后，init 监听
            initListener();
        } catch (NacosException e) {
            log.error("init nacos config service error", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void destroy() throws Exception {
        configService.shutDown();
    }

    public void publishConfig(String dataId, T config) {
        try {
            String content = mapper.writeValueAsString(config);
            String group = nacosConfigProperties.getGroup();
            String type = ConfigType.JSON.getType();

            // 发布配置
            configService.publishConfig(dataId, group, content, type);

            // 添加监听器
            configService.addListener(dataId, group, new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String configInfo) {
                    log.info("receive config info: {}", configInfo);
                    try {
                        JobConfig config = mapper.readValue(configInfo, JobConfig.class);
                        // 发布配置变更事件
                        applicationContext.publishEvent(new JobConfigChangeEvent(this, config));
                    } catch (JsonProcessingException e) {
                        log.error("receive config error", e);
                        throw new RuntimeException(e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("publish config error", e);
            throw new RuntimeException(e);
        }
    }

    private void initListener() {

    }

}
