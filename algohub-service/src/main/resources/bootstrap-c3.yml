spring:
  jackson:
    time-zone: "Asia/Shanghai"
    date-format: yyyy-MM-dd HH:mm:ss
  application:
    name: algohub-service
  cloud:
    nacos:
      config:
        #nacos服务端地址，记住一定要加端口号80，要不然会默认映射到8848
        server-addr: http://cnbj1-nacos.api.xiaomi.net:80
        username: growth_featurestore_prod   #nacos的用户名和密码
        password@kc-sid: growth_algo_platform_sid
        password: GDBhozBkI19Preu0slQgGKgr5r8DhD6Ycg7oqH6HG8peaweC6giGODRAVvfm+15CPHwYEvE96IgcNEiIl5h5DoJ8usVOARgQab8MiqXeScCFk6neUVkGRhgU2YQBZJ6n8cR5B2FtbxHUsPiy6EsA
        namespace: growth_featurestore_prod
        group: feature-store.feature-service    #配置中心的组配置，默认为DEFAULT_GROUP