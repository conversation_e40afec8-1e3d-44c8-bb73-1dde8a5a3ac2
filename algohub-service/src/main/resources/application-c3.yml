spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      hikari: # 全局hikariCP参数，所有值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        catalog:
        connection-timeout: 30000
        validation-timeout: 2000
        idle-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 20
        min-idle: 5
        initialization-fail-timeout: 20
        connection-test-query: SELECT 1
      datasource:
        master:
          url: **************************************************************************************************************
          # 账号和密码是keyCenter加密后的密文
          username@kc-sid: growth_algo_platform_sid
          username: GBD9Pw7OewAjnCVEkJfSdZMcGBIfwXzCkG1GRaKjMtSzXz9Y5wEYEBtRQE8+mUElmVGXNCgDzP4YFInm4t2UPLVKRBKIy/bVEkxTBxPOAA==
          password@kc-sid: growth_algo_platform_sid
          password: GDA8GjzAUhCI3/5003WKYAcy1uGzXWytbygs84ZDqvTjOHATiyJhecXfMFGxvJ5qcCcYEoTI01MkN0LthCp0S3uXA9DRARgQ/8u1hU3cRVSN1qeoLAe9lxgU8lySbSdQDUzJcncAejlYNSJtNuAA
          driver-class-name: com.mysql.cj.jdbc.Driver
  jackson:
    deserialization:
      fail_on_unknown_properties: false
  task:
    execution:
      pool:
        core-size: 16
        max-size: 50
        queue-capacity: 1000
      thread-name-prefix: async-
  cloud:
    nacos:
      discovery:
        #nacos服务端地址，记住一定要加端口号80，要不然会默认映射到8848
        server-addr: http://cnbj1-nacos.api.xiaomi.net:80
        username: growth_featurestore_prod   #nacos的用户名和密码
        password@kc-sid: growth_algo_platform_sid
        password: GDBhozBkI19Preu0slQgGKgr5r8DhD6Ycg7oqH6HG8peaweC6giGODRAVvfm+15CPHwYEvE96IgcNEiIl5h5DoJ8usVOARgQab8MiqXeScCFk6neUVkGRhgU2YQBZJ6n8cR5B2FtbxHUsPiy6EsA
        namespace: growth_featurestore_prod
        group: feature-store.feature-service    #配置中心的组配置，默认为DEFAULT_GROUP