spring:
  profiles:
    active: @spring_profiles_active@
  jackson:
    time-zone: "Asia/Shanghai"
    date-format: yyyy-MM-dd HH:mm:ss
  application:
    name: algohub-service

server:
  port: 8809
  servlet:
    context-path: /
logging:
  level:
    com.mib.growth.engine: info
  config: classpath:logback-spring.xml
  file:
    path: /home/<USER>/log/algohub-service

data-factory:
  api:
    token: bdccd726aa4843dd934799e49a413566
    domain: https://api-gateway.dp.pt.xiaomi.com
    oncall: oc_edb59bf11d31be99fba8c7cd67c7088e
    spark-jar:
      create-uri: /openapi/develop/jobs/op/sparkalpha                    # 创建spark任务
      update-uri: /openapi/develop/jobs/{jobId}/op/sparkalpha            # 更新spark任务
    workflow:
      create-uri: /openapi/develop/workflow                              # 创建工作流
      update-uri: /openapi/develop/workflow/{workflowId}                 # 更新工作流
      get-base-uri: /openapi/develop/workflow/{workflowId}/base          # 获取工作流基础信息
      get-detail-uri: /openapi/develop/workflow/{workflowId}             # 获取工作流详情
      online-uri: /openapi/develop/workflow/{workflowId}/online          # 上线工作流
      offline-uri: /openapi/develop/workflow/{workflowId}/offline        # 下线工作流
      start-uri: /openapi/develop/workflow/{workflowId}/start             # 启动工作流
      stop-uri: /openapi/develop/workflow/{workflowId}/stop               # 停止工作流
    table:
      get-uri: /openapi/metadata/table/get                                # 获取表详情
      update-uri: /openapi/metadata/table/table/update                    # 更新表
    schema:
      get-list-uri: /openapi/schema/list                                 # 获取schema列表
      get-uri: /openapi/schema/get                                      # 获取单个schema详情
    job:
      get-uri: /openapi/develop/jobs/{jobId}                              # 获取任务详情
dac:
  api:
    domain: https://api-data.dt.mi.com
    column-get-uri: /open-apis/dac/v1/columns/${guid}                     # 获取表字段信息
    search-table-uri: /open-apis/dac/v1/map/searchTable                   # 搜索表
