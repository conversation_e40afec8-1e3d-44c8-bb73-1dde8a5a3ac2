spring:
  jackson:
    time-zone: "Asia/Shanghai"
    date-format: yyyy-MM-dd HH:mm:ss
  application:
    name: algohub-service
  cloud:
    nacos:
      config:
        #nacos服务端地址，记住一定要加端口号80，要不然会默认映射到8848
        server-addr: http://dev-nacos.api.xiaomi.net:80
        username: growth_featurestore_dev   #nacos的用户名和密码
        password@kc-sid: growth_algo_platform_sid
        password: GDCLEBMS++UGQng+INJl/N9KZtAZicwA3VWktDByFF7WdtXA3t6yRCqqkGlNRcKg75QYEiMjDrhcoUluseNiaFJw3Q8O/xgQaSnXO05ZRD2AtPdEx8chfhgUjRAkv1+kjgtvywmg9ScvTtjaOvAA
        namespace: growth_featurestore_dev
        group: feature-store.feature-service    #配置中心的组配置，默认为DEFAULT_GROUP