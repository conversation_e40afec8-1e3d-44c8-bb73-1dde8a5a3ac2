<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台设置 -->
    <springProperty scope="context" name="LOG_PATH" source="logging.path"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd_HH:mm:ss.SSS} %-5level [%X{traceId}] %t %logger{30}.%M\(%L\) [%m]%n</pattern>
        </encoder>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/algohub-service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/algohub-service.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd_HH:mm:ss.SSS} %-5level [%X{traceId}] %t %logger{30}.%M\(%L\) [%m]%n</pattern>
        </encoder>
    </appender>


    <appender name="stdFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/algohub-service.stdout.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/algohub-service.stdout.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd_HH:mm:ss.SSS} %-5level [%X{traceId}] %t %logger{30}.%M\(%L\) [%m]%n</pattern>
        </encoder>
    </appender>

    <logger name="com.xiaomi.growth.feature" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="FILE"/>
    </logger>
    <root level="INFO">
        <appender-ref ref="stdFileAppender"/>
    </root>
</configuration>