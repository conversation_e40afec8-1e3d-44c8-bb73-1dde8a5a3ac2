spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      hikari: # 全局hikariCP参数，所有值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        catalog:
        connection-timeout: 30000
        validation-timeout: 2000
        idle-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 20
        min-idle: 5
        initialization-fail-timeout: 20
        connection-test-query: SELECT 1
      datasource:
        master:
          url: *****************************************************************************************************************************
          # 账号和密码是keyCenter加密后的密文
          username@kc-sid: growth_algo_platform_sid
          username: GCCWgH21A4PhOhJ-JW_GM1iVX7eWrNby-YyultQQJ0aVmRgSrk6IYDWvSpKKaRqozcP9XkT_GBBG7ARCzvpNM6HgqEqfPTv2GBRTMzBjvyJWoh7wFx6exfoajRMQEAA
          password@kc-sid: growth_algo_platform_sid
          password: GDBWtGUm-yxyK7oJbXaj6cqq2fgyiMqEV2dwmLpKO2yfk633AW17iXmg_SLYPueMZM8YEgqlJtFWSUeJkAeeGnYItSAT_xgQ82ja3bW6Ql2P0gJdishldhgUb9O34kwEEhghnW6lAA3V69vaQ4cA
          driver-class-name: com.mysql.cj.jdbc.Driver
  jackson:
    deserialization:
      fail_on_unknown_properties: false
  task:
    execution:
      pool:
        core-size: 16
        max-size: 50
        queue-capacity: 1000
      thread-name-prefix: async-
  cloud:
    nacos:
      discovery:
        #nacos服务端地址，记住一定要加端口号80，要不然会默认映射到8848
        server-addr: http://dev-nacos.api.xiaomi.net:80
        username: growth_featurestore_dev   #nacos的用户名和密码
        password@kc-sid: growth_algo_platform_sid
        password: GDCLEBMS++UGQng+INJl/N9KZtAZicwA3VWktDByFF7WdtXA3t6yRCqqkGlNRcKg75QYEiMjDrhcoUluseNiaFJw3Q8O/xgQaSnXO05ZRD2AtPdEx8chfhgUjRAkv1+kjgtvywmg9ScvTtjaOvAA
        namespace: growth_featurestore_dev
        group: feature-store.feature-service    #配置中心的组配置，默认为DEFAULT_GROUP